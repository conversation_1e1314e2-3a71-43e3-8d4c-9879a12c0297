{"name": "uc-fmo", "version": "1.0.0", "description": "UC Facility Management Office System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "repository": {"type": "git", "url": "git+https://github.com/KATHLEYNN/UC-FMO.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/KATHLEYNN/UC-FMO/issues"}, "homepage": "https://github.com/KATHLEYNN/UC-FMO#readme", "dependencies": {"bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.0.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.1", "puppeteer": "^22.0.0"}, "devDependencies": {"nodemon": "^3.1.10"}}