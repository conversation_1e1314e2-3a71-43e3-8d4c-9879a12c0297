* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  background: linear-gradient(135deg, #f0f4f1 0%, #e8f5e9 100%);
  min-height: 100vh;
}

.rules-container {
  max-width: 900px;
  margin: 2rem auto;
  padding: 0 1rem;
}

.rules-content {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.rules-header {
  background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%);
  color: white;
  text-align: center;
  padding: 2.5rem 2rem;
}

.rules-header i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.9;
}

.rules-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.rules-header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

.rules-body {
  padding: 2.5rem;
}

.rule-item {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f8fdf8;
  border-radius: 12px;
  border-left: 4px solid #2e7d32;
}

.rule-item h3 {
  color: #2e7d32;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.rule-item ul {
  list-style: none;
  padding-left: 0;
}

.rule-item li {
  margin-bottom: 0.75rem;
  padding-left: 1.5rem;
  position: relative;
  color: #333;
  line-height: 1.6;
}

.rule-item li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #2e7d32;
  font-weight: bold;
}

.rules-footer {
  padding: 2rem 2.5rem;
  background: #f9f9f9;
  border-top: 1px solid #e0e0e0;
}

.acceptance-checkbox {
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.acceptance-checkbox input[type="checkbox"] {
  width: 20px;
  height: 20px;
  accent-color: #2e7d32;
}

.acceptance-checkbox label {
  font-size: 1.1rem;
  color: #333;
  cursor: pointer;
  line-height: 1.5;
}

.rules-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.btn-decline, .btn-accept {
  padding: 0.875rem 2rem;
  border: none;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-decline {
  background: #f44336;
  color: white;
}

.btn-decline:hover {
  background: #d32f2f;
  transform: translateY(-2px);
}

.btn-accept {
  background: #2e7d32;
  color: white;
}

.btn-accept:hover:not(:disabled) {
  background: #1b5e20;
  transform: translateY(-2px);
}

.btn-accept:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

/* Venue Selection Styles */
.venue-container {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 1rem;
}

.venue-content {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.venue-header {
  background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%);
  color: white;
  text-align: center;
  padding: 2rem;
}

.venue-header i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.9;
}

.venue-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.venue-header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

.venue-grid {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 2rem;
  background: #f8f9fa;
}

.campus-section {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.campus-title {
  color: #2e7d32;
  font-size: 1.3rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border-bottom: 2px solid #e8f5e8;
  padding-bottom: 0.5rem;
}

.campus-venues {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.venue-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.venue-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  border-color: #e8f5e8;
}

.venue-card.selected {
  background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
  color: white;
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(76, 175, 80, 0.3);
  border-color: #4caf50;
}

.venue-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #2e7d32;
  transition: color 0.3s ease;
}

.venue-card.selected .venue-icon {
  color: white;
}

.venue-card h3,
.venue-card h4 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #333;
  transition: color 0.3s ease;
}

.venue-card.selected h3,
.venue-card.selected h4 {
  color: white;
}

.venue-card p {
  font-size: 0.95rem;
  color: #666;
  line-height: 1.4;
  transition: color 0.3s ease;
}

.venue-card.selected p {
  color: rgba(255, 255, 255, 0.9);
}

.venue-footer {
  padding: 2rem;
  background: #f9f9f9;
  border-top: 1px solid #e0e0e0;
}

.selected-venue-info {
  text-align: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: #e8f5e9;
  border-radius: 8px;
  color: #2e7d32;
  font-size: 1.1rem;
}

.selected-venue-info i {
  margin-right: 0.5rem;
  color: #4caf50;
}

.venue-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.calendar-container {
  max-width: 1000px;
  margin: 2rem auto;
  padding: 0 1rem;
}

.calendar-content {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.calendar-header {
  background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%);
  color: white;
  text-align: center;
  padding: 2rem;
}

.calendar-header h2 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.calendar-header p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 1.5rem;
}

.calendar-navigation {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
}

.nav-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 0.75rem;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

#current-month-year {
  font-size: 1.3rem;
  font-weight: 600;
  min-width: 200px;
}

.calendar-legend {
  display: flex;
  justify-content: center;
  gap: 2rem;
  padding: 1.5rem;
  background: #f9f9f9;
  border-bottom: 1px solid #e0e0e0;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #666;
}

.legend-circle {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid #ddd;
}

.legend-circle.today {
  background-color: #2e7d32;
  border-color: #2e7d32;
}

.legend-circle.available {
  background-color: white;
  border-color: #ccc;
}

.legend-circle.reserved {
  background-color: #f44336;
  border-color: #f44336;
}

.legend-circle.selected {
  background-color: #ff9800;
  border-color: #ff9800;
}

/* Venue Filter Styles */
.venue-filter-container {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin: 1rem 0;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 1000px;
  margin: 1rem auto;
}

.venue-filter-container label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
}

.venue-filter-container label i {
  color: #4caf50;
}

.venue-filter-select {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 2px solid #e0e0e0;
  border-radius: 6px;
  font-size: 1rem;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.venue-filter-select:focus {
  outline: none;
  border-color: #4caf50;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.venue-filter-select:hover {
  border-color: #4caf50;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, minmax(120px, 1fr));
  gap: 4px;
  background: #f5f5f5;
  padding: 1rem;
  border-radius: 8px;
  max-width: 1000px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .calendar-grid {
    grid-template-columns: repeat(7, minmax(90px, 1fr));
    gap: 2px;
    padding: 0.5rem;
  }

  .calendar-day {
    min-height: 90px;
    max-height: 90px;
    padding: 0.25rem;
  }

  .day-number {
    font-size: 0.75rem;
    top: 0.15rem;
    right: 0.25rem;
    padding: 0.05rem 0.2rem;
  }

  .event-title {
    font-size: 0.55rem;
    margin-top: 1.2rem;
    max-height: 1.8rem;
  }

  .event-location {
    font-size: 0.5rem;
    padding: 0.1rem 0.2rem;
  }
}

.day-header {
  background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
  color: white;
  text-align: center;
  padding: 0.8rem;
  font-weight: 600;
  font-size: 0.9rem;
  border-radius: 6px 6px 0 0;
  box-shadow: 0 2px 4px rgba(46, 125, 50, 0.2);
}

.calendar-day {
  background: white;
  text-align: center;
  padding: 0.4rem;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 110px;
  max-height: 110px;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  font-weight: 500;
  flex-direction: column;
  position: relative;
  border-radius: 6px;
  overflow: hidden;
}

.calendar-day:hover:not(.disabled):not(.reserved) {
  background: #e8f5e8;
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.12);
}

.calendar-day:not(.reserved) {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.calendar-day.today {
  background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
  color: white;
  box-shadow: 0 3px 8px rgba(46, 125, 50, 0.3);
  border: 2px solid #4caf50;
}

.calendar-day.reserved {
  background: linear-gradient(135deg, #ff7043 0%, #ff5722 100%);
  color: white;
  cursor: not-allowed;
  padding: 0.3rem;
  justify-content: space-between;
  align-items: stretch;
  box-shadow: 0 2px 4px rgba(255, 87, 34, 0.3);
  border: 1px solid #ff5722;
}

.day-number {
  font-size: 0.9rem;
  font-weight: 700;
  position: absolute;
  top: 0.2rem;
  right: 0.3rem;
  background: rgba(255, 255, 255, 0.25);
  padding: 0.1rem 0.25rem;
  border-radius: 3px;
  min-width: 1.2rem;
  text-align: center;
  z-index: 2;
}

.event-title {
  font-size: 0.65rem;
  font-weight: 600;
  line-height: 1.1;
  margin-top: 1.5rem;
  margin-bottom: 0.1rem;
  text-align: left;
  word-wrap: break-word;
  flex: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  max-height: 2.2rem;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.event-location {
  font-size: 0.55rem;
  opacity: 0.95;
  line-height: 1;
  text-align: left;
  word-wrap: break-word;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.15rem 0.25rem;
  border-radius: 2px;
  font-weight: 500;
  max-height: 1.1rem;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.calendar-day.selected {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
  color: white;
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(255, 152, 0, 0.4);
  border: 2px solid #ff9800;
}

.calendar-day.disabled {
  background: #fafafa;
  color: #bdbdbd;
  cursor: not-allowed;
  opacity: 0.6;
}

.calendar-footer {
  padding: 2rem;
  background: #f9f9f9;
  border-top: 1px solid #e0e0e0;
}

.selected-date-info {
  text-align: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: #e8f5e9;
  border-radius: 8px;
  color: #2e7d32;
  font-size: 1.1rem;
}

.calendar-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.btn-back, .btn-continue {
  padding: 0.875rem 2rem;
  border: none;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-back {
  background: #757575;
  color: white;
}

.btn-back:hover {
  background: #616161;
  transform: translateY(-2px);
}

.btn-continue {
  background: #2e7d32;
  color: white;
}

.btn-continue:hover:not(:disabled) {
  background: #1b5e20;
  transform: translateY(-2px);
}

.btn-continue:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.form-selection-container {
  max-width: 900px;
  margin: 2rem auto;
  padding: 0 1rem;
}

.form-selection-content {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 3rem;
  text-align: center;
}

.form-selection-content h2 {
  color: #2e7d32;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.form-selection-content p {
  color: #666;
  font-size: 1.1rem;
  margin-bottom: 2.5rem;
}

.form-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.form-option-card {
  background: white;
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  padding: 2rem;
  text-decoration: none;
  color: #333;
  transition: all 0.3s ease;
  text-align: center;
}

.form-option-card:hover {
  border-color: #2e7d32;
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(46, 125, 50, 0.2);
}

.form-option-card i {
  font-size: 3rem;
  color: #2e7d32;
  margin-bottom: 1rem;
}

.form-option-card h3 {
  color: #2e7d32;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.form-option-card p {
  color: #666;
  font-size: 0.95rem;
  line-height: 1.5;
  margin: 0;
}

@media (max-width: 768px) {
  .rules-container,
  .calendar-container,
  .form-selection-container {
    margin: 1rem auto;
    padding: 0 0.5rem;
  }

  .rules-header,
  .calendar-header {
    padding: 1.5rem 1rem;
  }

  .rules-body,
  .calendar-footer,
  .rules-footer {
    padding: 1.5rem;
  }

  .form-selection-content {
    padding: 2rem 1.5rem;
  }

  .calendar-legend {
    flex-wrap: wrap;
    gap: 1rem;
  }

  .calendar-navigation {
    gap: 1rem;
  }

  #current-month-year {
    font-size: 1.1rem;
    min-width: 150px;
  }

  .form-options {
    grid-template-columns: 1fr;
  }

  .rules-actions,
  .calendar-actions {
    flex-direction: column;
    align-items: center;
  }

  .btn-decline, .btn-accept,
  .btn-back, .btn-continue {
    width: 100%;
    max-width: 300px;
  }
}
