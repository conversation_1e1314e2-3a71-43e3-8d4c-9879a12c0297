<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>My Forms - UC FMO</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="/utils/navbar.css">
    <link rel="stylesheet" href="/utils/modals.css">
    <link rel="stylesheet" href="/css/components/modern-filter.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .page-header {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .page-header h1 {
            margin: 0;
            color: #2e7d32;
            font-size: 28px;
        }
        .page-header p {
            margin: 10px 0 0 0;
            color: #666;
        }
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-card .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #2e7d32;
        }
        .stat-card .stat-label {
            color: #666;
            font-size: 14px;
            margin-top: 5px;
        }
        .filters {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 25px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            align-items: end;
        }
        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .filter-group label {
            font-weight: 600;
            color: #333;
            font-size: 14px;
        }
        .filter-group select,
        .filter-group input {
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
            background: white;
        }
        .filter-group select:focus,
        .filter-group input:focus {
            outline: none;
            border-color: #2e7d32;
        }
        .filter-actions {
            display: flex;
            gap: 10px;
        }
        .forms-grid {
            display: grid;
            gap: 20px;
        }
        .form-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
            transition: transform 0.2s;
            border-left: 4px solid #2e7d32;
        }
        .form-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .form-card.campus {
            border-left-color: #2e7d32;
        }
        .form-card.internal {
            border-left-color: #1976d2;
        }
        .form-card.external {
            border-left-color: #f57c00;
        }
        .form-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }
        .form-title {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }
        .form-type-badge {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }
        .form-type-badge.internal {
            background: #e3f2fd;
            color: #1976d2;
        }
        .form-type-badge.external {
            background: #fff3e0;
            color: #f57c00;
        }
        .form-details {
            margin-bottom: 15px;
        }
        .form-detail {
            display: flex;
            margin-bottom: 8px;
            font-size: 14px;
        }
        .form-detail strong {
            min-width: 120px;
            color: #555;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        .status-approved {
            background: #d4edda;
            color: #155724;
        }
        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }
        .form-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        .btn-primary {
            background: #2e7d32;
            color: white;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn:hover {
            opacity: 0.9;
        }
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .empty-state img {
            width: 120px;
            opacity: 0.5;
            margin-bottom: 20px;
        }
        .loading {
            text-align: center;
            padding: 40px;
        }
        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #2e7d32;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 30px;
        }
        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }
        .pagination button:hover:not(:disabled) {
            background: #f8f9fa;
        }
        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .pagination .current-page {
            background: #2e7d32;
            color: white;
            border-color: #2e7d32;
        }
        .footer {
            background-color: #033e2e;
            color: white;
            text-align: center;
            padding: 20px;
            font-size: 14px;
            margin-top: 50px;
        }
    </style>
</head>
<body>
    <!-- Navbar Container -->
    <div id="navbar-container"></div>

    <div class="container">
        <div class="page-header">
            <h1><i class="fas fa-file-alt"></i> My Forms</h1>
            <p>View and manage all your submitted forms (SARF, Internal Client, External Client)</p>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-cards" id="statsCards">
            <div class="stat-card">
                <div class="stat-number" id="totalForms">0</div>
                <div class="stat-label">Total Forms</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="pendingForms">0</div>
                <div class="stat-label">Pending</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="acceptedForms">0</div>
                <div class="stat-label">Accepted</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="rejectedForms">0</div>
                <div class="stat-label">Rejected</div>
            </div>
        </div>

        <!-- Modern Filter Component -->
        <div id="modernFilter"></div>

        <!-- Quick Actions -->
        <div style="text-align: right; margin-bottom: 20px;">
            <a href="/user/reservation" class="btn btn-primary">
                <i class="fas fa-plus"></i> Create New Form
            </a>
        </div>

        <div id="formsContainer">
            <div class="loading">
                <div class="loading-spinner"></div>
                <p>Loading your forms...</p>
            </div>
        </div>

        <div id="pagination" class="pagination" style="display: none;"></div>
    </div>

    <footer class="footer">
        © 2025 University of the Cordilleras. All Rights Reserved.
    </footer>

    <script>
        // Global variables
        let formsManager;
        let modernFilter;

        // Initialize the page
        function initializePage() {
            // Initialize Forms Manager
            formsManager = new FormsManager({
                apiEndpoint: '/api/sarf/my-all-forms',
                itemsPerPage: 4,
                isAdmin: false,
                containerId: 'formsContainer',
                paginationId: 'pagination',
                statsEnabled: true
            });

            // Initialize Modern Filter
            modernFilter = new ModernFilter('modernFilter', {
                showSearch: true,
                showStatus: true,
                showType: true,
                showDateRange: false,
                placeholder: 'Search your forms...',
                onFilter: (filters) => {
                    formsManager.applyFilters(filters);
                },
                onClear: () => {
                    formsManager.applyFilters({});
                }
            });

            // Load initial data
            formsManager.updateStats();
            formsManager.loadForms();
        }

        // Utility functions for PDF operations

        // Preview PDF
        function previewPDF(pdfUrl) {
            window.open(pdfUrl, '_blank');
        }

        // Download form PDF
        async function downloadFormPDF(formId, formType) {
            try {
                const token = localStorage.getItem('token');
                let downloadUrl = '';

                // Determine the correct download endpoint based on form type
                if (formType === 'campus') {
                    downloadUrl = `/api/sarf/download/${formId}`;
                } else if (formType === 'internal') {
                    downloadUrl = `/api/sarf/internal/download/${formId}`;
                } else if (formType === 'external') {
                    downloadUrl = `/api/sarf/external/download/${formId}`;
                }

                const response = await fetch(downloadUrl, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `${formType.toUpperCase()}-Form-${formId}.pdf`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                } else {
                    const error = await response.json();
                    alert('Error downloading PDF: ' + error.message);
                }
            } catch (error) {
                console.error('Error downloading PDF:', error);
                alert('Failed to download PDF. Please try again.');
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            const token = localStorage.getItem('token');
            if (!token) {
                window.location.href = '/login';
                return;
            }

            // Verify user role and initialize navbar
            fetch('/api/auth/verify', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            })
            .then(response => response.json())
            .then(data => {
                if (!data.user) {
                    window.location.href = '/login';
                    return;
                }

                // Allow admin, student, and external users
                if (['admin', 'student', 'external'].includes(data.user.role)) {
                    initializeNavbar('user', 'my-forms', data.user.role);
                    initializePage();
                } else {
                    window.location.href = '/login';
                }
            })
            .catch(error => {
                window.location.href = '/login';
            });
        });
    </script>
    <script src="/utils/navbar.js"></script>
    <script src="/utils/modals.js"></script>
    <script src="/js/components/modern-filter.js"></script>
    <script src="/js/utils/forms-manager.js"></script>
</body>
</html>
