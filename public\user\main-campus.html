<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>UC Campus - Main Campus</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
  <link rel="stylesheet" href="/utils/navbar.css">
</head>
<body>

  <!-- Navbar Container -->
  <div id="navbar-container"></div>

  <main class="home">
    <section class="main">
      <h1>Main Campus</h1>
      <p>Gov. Pack Rd, Baguio City</p>
    </section>
    <section class="main-image">
      <img src="https://lh3.googleusercontent.com/gps-cs-s/AC9h4np7Tc3yzeKy6iOC5Q0_jeN-J6Z6evrxlbTcjhGmiismCJzawz41zpOJkPowWF2liuR-bFDU21c9KGEb_ZyzAQaazJKPEVhvkhbC586jeCtL2qhARrP8JIrxXsKmlJ1M91OCekE=s680-w680-h510-rw" alt="Main Image">
    </section>
  </main>

  <main class="home">
  <h1>Select Main Campus Facility</h1>
  <div class="reservation-options">
    <div class="option-card" onclick="openModal('Auditorium')">Auditorium</div>
    <div class="option-card" onclick="openModal('Cañao Hall')">Cañao Hall</div>
    <div class="option-card" onclick="openModal('Conference Room')">Conference Room</div>
    <div class="option-card" onclick="openModal('Gymnasium')">Gymnasium</div>
    <div class="option-card" onclick="openModal('Laboratory Room')">Laboratory Room</div>
    <div class="option-card" onclick="openModal('Lecture Room')">Lecture Room</div>
    <div class="option-card" onclick="openModal('Training Center')">Training Center</div>
  </div>

    <!-- Modal -->
  <div id="facilityModal" class="modal">
    <div class="modal-content">
      <span class="close" onclick="closeModal()">&times;</span>

   <!-- Facility Image -->
    <img id="facilityImage" src="" alt="Facility Image" class="modal-image" />

      <h2 id="facilityTitle">Facility Name</h2>
      <p><strong>Building:</strong> <span id="building"></span></p>
      <p><strong>Floor:</strong> <span id="floor"></span></p>
      <p><strong>Rooms:</strong> <span id="room"></span></p>
      <p><strong>Amenities:</strong> <span id="amenities"></span></p>
      <p><strong>Technical Equipment:</strong> <span id="equipment"></span></p>
      <p><strong>Max Capacity:</strong> <span id="capacity"></span></p>
      <p><strong>Possible Events:</strong> <span id="events"></span></p>
    </div>
  </div>
</main>

<script>
  const facilityData = {
    "Auditorium": {
      image: "https://pbs.twimg.com/media/D7TGM5xU0AA_UMg?format=jpg&name=large",
      building: "EDS Building",
      floor: "9th Floor",
      amenities: "Monoblock chairs ",
      equipment: "Projector, PA System, Microphone",
      capacity: "250",
      events: "Review Center, Institutional Events"
    },
    "Cañao Hall": {
      image: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSN77_6EnJ8qcz-kpSj9H8farLq8hCyiXTxDw&s",
      building: "CHTM Building",
      floor: "10th Floor",
      amenities: "Long tables, white monoblock chairs",
      equipment: "Projector, PA System, Microphone",
      capacity: "100-150",
      events: "Laboratory Classes, Meeting, Institutional Events",
    },
    "Conference Room": {
      image: "https://www.oyorooms.com/blog/wp-content/uploads/2018/03/proper-seating-arrangement.jpg",
      building: "Main Building",
      floor: "4th Floor",
      amenities: "Brown Tables, Swivel Chairs",
      equipment: "Projector",
      capacity: "20",
      events: "Meeting, Institutional Events"
    },
    "Gymnasium": {
      image: "https://con.uc-bcf.edu.ph/wp-content/uploads/sites/12/2024/10/Unknown.jpg",
      building: "Science Building",
      floor: "3rd Floor",
      equipment: "LED Wall, PA System, Microphones",
      capacity: "1200",
      events: "PE Classes, Sports, Graduation"
    },
    "Laboratory Room": {
      image: "https://bigsee.eu/wp-content/uploads/2021/01/naslovna-77.jpg",
      building: "Main Building, Science Building, EDS Building, BRS Building, CHTM Building, P.E. Building",
      floor: "Basement, 1st Floor, 2nd Floor, 3rd Floor, 4th Floor, 5th Floor, 6th Floor, 7th Floor, 9th Floor, 10th Floor,",
      rooms: "Computer, Hydro, Matls, Physics, GS & JHS, HE, Chemistry, TLE, SHS Physics, CEA Comp., Engr. Comp., Electronic and Digital, Biology, CISCO, Drafting, Nutrition, Nursing, E-Learning, Speech, CON Health Center, Masscom, Psychology, FA Comp., Animation, Mootcourt, Tribu Cafeteria, Hotel, Culinary Studio, Kitchen, Creativity Hall, Dancing Hall, Firing Range ",
      capacity: "1-100",
    },
    "Lecture Room": {
      image: "https://www.uc-bcf.edu.ph/assets/images/research/tbi-e20200211.png",
      building: "Academic Center",
      floor: "2nd Floor",
      amenities: "Whiteboard, Chairs",
      equipment: "Projector, Speakers",
      capacity: "60",
      events: "Lectures, Classes",
    },
    "Training Center": {
      image: "https://www.ashrae.org/image%20library/main%20nav/about/new%20hq/website_gowantrainingcenter-800x600.jpg",
      building: "BRS Building",
      floor: "4th Floor",
      amenities: "Brown Tables, Monoblock chairs",
      equipment: "Projector, PA System",
      capacity: "35-40",
      events: "Meeting, Institutional Events"
    }
  };

  function openModal(facility) {
    const data = facilityData[facility];
    document.getElementById("facilityTitle").innerText = facility;
    document.getElementById("facilityImage").src = data.image;
    document.getElementById("building").innerText = data.building;
    document.getElementById("floor").innerText = data.floor;
    document.getElementById("room").innerText = data.room;
    document.getElementById("amenities").innerText = data.amenities;
    document.getElementById("equipment").innerText = data.equipment;
    document.getElementById("capacity").innerText = data.capacity;
    document.getElementById("events").innerText = data.events;
    document.getElementById("facilityModal").style.display = "block";
  }

  function closeModal() {
    document.getElementById("facilityModal").style.display = "none";
  }
  document.addEventListener('DOMContentLoaded', function() {
    const token = localStorage.getItem('token');
    if (!token) {
      window.location.href = '/login';
      return;
    }

    fetch('/api/auth/verify', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    .then(response => response.json())
    .then(data => {
      if (!data.user) {
        window.location.href = '/login';
        return;
      }

      if (isAdminRole(data.user.role)) {
        initializeNavbar('admin', 'campus', data.user.role);
      } else if (data.user.role === 'student' || data.user.role === 'external') {
        initializeNavbar('user', 'campus', data.user.role);
      } else {
        window.location.href = '/login';
      }
    })
    .catch(error => {
      window.location.href = '/login';
    });
  });
</script>


  <footer class="footer">
  © 2025 University of the Cordilleras. All Rights Reserved.
</footer>

  <style>
    * {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', sans-serif;
  background-color: #f0f4f3;
}

nav {
      position: sticky;
      top: 0;
      z-index: 999;
      background-color: #2e7d32;
      color: #ffffff;
      padding: 15px 30px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

.nav-links {
  list-style: none;
  display: flex;
  gap: 2rem;
}

.nav-links li a {
  text-decoration: none;
  color: white;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-links li a:hover {
  color: #a5d6a7; 
}

.logo img {
  height: 75px;
  width: auto;
}

.home {
  text-align: center;
  padding: 3rem 2rem;
  background-color: #e8f5e9;
}

.main h1 {
  font-size: 2.5rem;
  color: #1b5e20;
  margin-bottom: .5rem;
}

.main p {
  font-size: 1.2rem;
  color: #4e944f;
  margin-bottom: 1rem;
}

h1 {
  font-size: 2.5rem;
  margin-bottom: 40px;
  color: #256d1b;
}

.reservation-options {
  display: flex;
  flex-direction: row; 
  align-items: center;
  gap: 30px;
}

.option-card {
  background-color: #ffffff;
  padding: 40px 30px;
  width: 250px;
  border-radius: 12px;
  text-decoration: none;
  color: #1b5e20;
  font-weight: 600;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #c5e1a5;
}

.option-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 24px rgba(67, 160, 71, 0.2);
  background-color: #e8f5e9;
  color: #2e7d32;
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 99;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 64, 0, 0.6);
  backdrop-filter: blur(3px);
}

.modal-content {
  background-color: #f1f8e9;
  margin: 10% auto;
  padding: 20px;
  border: 1px solid #c5e1a5;
  width: 80%;
  max-width: 600px;
  border-radius: 12px;
  color: #2e7d32;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.close {
  color: #1b5e20;
  float: right;
  font-size: 24px;
  font-weight: bold;
  cursor: pointer;
}

.modal-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 20px;
}

.modal-info {
  display: grid;
  grid-template-columns: 1fr;
  gap: 10px;
  font-size: 15px;
  color: #333;
}

.modal-info strong {
  color: #1b5e20;
}


.footer {
  background-color: #033E2E;
  color: white;
  text-align: center;
  padding: 20px;
  font-size: 14px;
}
  </style>
  <script src="/js/roleUtils.js"></script>
  <script src="/utils/navbar.js"></script>
</body>
</html>