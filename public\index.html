<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UC Campus - Welcome</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="/utils/navbar.css">
</head>
<body>
    <!-- Navbar Container -->
    <div id="navbar-container"></div>

    <main class="home">
        <section class="welcome">
            <h1>Welcome to the UC Campus Management Office</h1>
            <p>Effortlessly book classrooms, laboratories, and university facilities anytime, anywhere.</p>
            <div class="cta-buttons">
                <a href="/login" class="cta-button">Login</a>
                <a href="/signup" class="cta-button secondary">Sign Up</a>
            </div>
        </section>
    </main>

    <div class="features">
        <h2>Our Features</h2>
        <div class="feature-grid">
            <div class="feature-card">
                <i class="fas fa-calendar-alt"></i>
                <h3>Easy Booking</h3>
                <p>Book facilities with just a few clicks</p>
            </div>
            <div class="feature-card">
                <i class="fas fa-clock"></i>
                <h3>Real-time Availability</h3>
                <p>Check facility availability instantly</p>
            </div>
            <div class="feature-card">
                <i class="fas fa-building"></i>
                <h3>Multiple Campuses</h3>
                <p>Access facilities across all UC campuses</p>
            </div>
        </div>
    </div>

    <footer class="footer">
        © 2025 University of the Cordilleras. All Rights Reserved.
    </footer>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
        }

        .navbar {
            position: sticky;
            top: 0;
            z-index: 999;
            background-color: #2e7d32;
            color: #ffffff;
            padding: 15px 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .logo img {
            height: 75px;
            width: auto;
        }

        .nav-links {
            list-style: none;
            display: flex;
            gap: 2rem;
        }

        .nav-links li a {
            text-decoration: none;
            color: white;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links li a:hover {
            color: #a5d6a7;
        }

        .home {
            text-align: center;
            padding: 4rem 2rem;
            background-color: #e8f5e9;
            min-height: 60vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .welcome {
            max-width: 800px;
            margin: 0 auto;
        }

        .welcome h1 {
            font-size: 2.5rem;
            color: #1b5e20;
            margin-bottom: 1rem;
        }

        .welcome p {
            font-size: 1.2rem;
            color: #4e944f;
            margin-bottom: 2rem;
        }

        .cta-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        .cta-button {
            display: inline-block;
            padding: 1rem 2rem;
            border-radius: 30px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .cta-button:first-child {
            background-color: #2e7d32;
            color: white;
        }

        .cta-button.secondary {
            background-color: white;
            color: #2e7d32;
            border: 2px solid #2e7d32;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .features {
            padding: 4rem 2rem;
            background-color: white;
        }

        .features h2 {
            text-align: center;
            color: #1b5e20;
            margin-bottom: 3rem;
            font-size: 2rem;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .feature-card {
            text-align: center;
            padding: 2rem;
            background-color: #f8f9fa;
            border-radius: 10px;
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-card i {
            font-size: 2.5rem;
            color: #2e7d32;
            margin-bottom: 1rem;
        }

        .feature-card h3 {
            color: #1b5e20;
            margin-bottom: 0.5rem;
        }

        .footer {
            background-color: #1b5e20;
            color: white;
            text-align: center;
            padding: 1.5rem;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .welcome h1 {
                font-size: 2rem;
            }

            .welcome p {
                font-size: 1rem;
            }

            .cta-buttons {
                flex-direction: column;
                gap: 1rem;
            }

            .feature-grid {
                grid-template-columns: 1fr;
            }
        }

        .logout-btn {
            color: #fff !important;
            background-color: #d32f2f;
            padding: 8px 16px;
            border-radius: 4px;
        }

        .logout-btn:hover {
            background-color: #b71c1c;
            color: #fff !important;
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            initializeNavbar('public', 'home');

            const token = localStorage.getItem('token');
            if (token) {
                fetch('/api/auth/verify', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Token invalid');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.user) {
                        if (isAdminRole(data.user.role)) {
                            window.location.href = '/admin/admin';
                        } else {
                            window.location.href = '/home';
                        }
                    }
                })
                .catch(error => {
                    localStorage.removeItem('token');
                });
            }
        });

        async function handleLogout() {
            if (confirm('Are you sure you want to log out?')) {
                try {
                    const token = localStorage.getItem('token');
                    const response = await fetch('/api/auth/logout', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });

                    if (response.ok) {
                        localStorage.removeItem('token');
                        window.location.reload();
                    } else {
                        alert('Error logging out. Please try again.');
                    }
                } catch (error) {
                    console.error('Logout error:', error);
                    alert('Error logging out. Please try again.');
                }
            }
        }
    </script>
    <script src="/js/roleUtils.js"></script>
    <script src="/utils/navbar.js"></script>
</body>
</html>