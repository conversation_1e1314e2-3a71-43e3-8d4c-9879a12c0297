<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>On-Campus Registration Form</title>
    <style>
      .navbar {
        background-color: #2e7d32; /* dark green */
        color: white;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 2rem;
        position: relative;
      }
      .nav-links {
        list-style: none;
        display: flex;
        gap: 2rem;
      }

      .nav-links li a {
        text-decoration: none;
        color: white;
        font-weight: 500;
        font-size: 16px;
      }

      .nav-links li a:hover {
        color: #a5d6a7;
      }
      .footer {
        background-color: #033e2e;
        color: white;
        text-align: center;
        padding: 20px;
        font-size: 14px;
      }
      @page {
        size: 8.5in 13in;
        margin-left: 1cm;
        margin-right: 1cm;
        margin-top: 1cm;
        margin-bottom: 1cm;
      }
      @media print {
        .form-page {
          display: block !important;
        }
        .navbar,
        .footer,
        .navigation {
          display: none !important;
        }
      }
      body {
        font-family: Century Gothic, sans-serif;
        font-size: 12px;
        margin: 0;
        padding: 0;
      }
      .form-container {
        width: 100%;
        padding: 10px;
        box-sizing: border-box;
      }
      table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 10px;
      }
      td,
      th {
        border: 1px solid black;
        padding: 5px;
        vertical-align: top;
      }
      .no-border {
        border: none;
        border-collapse: collapse;
      }

      .no-border td,
      .no-border th {
        border: none;
        padding: 5px;
      }
      .form-title {
        background-color: #3535354d;
        text-align: center;
        font-weight: bold;
        padding: 8px;
        margin-bottom: 10px;
        border: 2px solid black;
        border-radius: 7px;
      }
      .two-columns {
        display: flex;
        gap: 10px;
        padding: 0;
        align-items: stretch;
      }

      .two-columns > div {
        flex: 1;
        min-width: 0;
      }

      .two-columns table {
        width: 100%;
      }
      .page-break {
        page-break-before: always;
      }
      .section-title {
        font-weight: bold;
        background-color: #3535354d;
      }
      .small {
        font-size: 10px;
      }
      .header-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;
      }
      .header-row img {
        height: 40px;
      }
      .form-page {
        display: none;
      }
      .form-page.active {
        display: block;
      }
      .navigation {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 20px;
      }
      button {
        padding: 6px 12px;
        font-size: 14px;
      }
      .full-form {
        max-width: 100%;
        margin: auto;
        font-family: system-ui, sans-serif;
        background-color: #f5f8f5;
        color: #1f1f1f;
        padding: 2rem;
        border-radius: 8px;
      }

      .full-form label {
        display: block;
        margin-bottom: 1.25rem;
        color: #2a4d2e;
        font-weight: 600;
        font-size: 1rem;
      }

      .full-form input,
      .full-form select,
      .full-form textarea {
        width: 100%;
        padding: 0.75rem;
        font-size: 1rem;
        margin-top: 0.25rem;
        box-sizing: border-box;
        color: #1a1a1a;
        background-color: #f5f5f5;
        border: 1px solid #ccc;
        border-radius: 4px;
      }

      .full-form input:focus,
      .full-form select:focus,
      .full-form textarea:focus {
        border-color: #4a7d4a;
        background-color: #f0f8f1;
        outline: none;
      }

      .full-form button {
        background-color: #4a7d4a;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
        border-radius: 4px;
        cursor: pointer;
        margin-top: 1rem;
        transition: background-color 0.2s ease;
      }

      .full-form button:hover {
        background-color: #386938;
      }

      .full-form input[type="checkbox"],
      .full-form input[type="radio"] {
        vertical-align: middle;
        transform: translateY(-1px);
        margin-right: 0.5rem;
        width: auto;
      }

      .step {
        display: none;
        animation: fade 0.25s linear forwards;
      }
      .step.active {
        display: block;
      }

      @keyframes fade {
        from {
          opacity: 0;
          transform: translateY(8px);
        }
        to {
          opacity: 1;
          transform: none;
        }
      }
      @media screen {
        #internalForm,
        #combinedForm {
          display: none;
        }
      }
      @media print {
        #userForm {
          display: none !important;
        }
        .form-page,
        #combinedForm {
          display: block !important;
        }
        .section-title {
          background-color: #3535354d;
          border: 1px solid black;
        }
      }
    </style>
  </head>

  <script>
    function onlyOne(checkbox, groupName) {
      const groupCheckboxes = document.querySelectorAll(
        `input[name="${groupName}"]`
      );
      groupCheckboxes.forEach((cb) => {
        if (cb !== checkbox) cb.checked = false;
      });
    }

    function toggleDependentInput(checkbox) {
      const targetId = checkbox.dataset.toggleTarget;
      if (!targetId) return;

      const target = document.getElementById(targetId);
      if (!target) return;

      if (checkbox.checked) {
        target.disabled = false;
      } else {
        target.disabled = true;
        if ("value" in target) target.value = "";
      }
    }
  </script>

<script>
    document.addEventListener("DOMContentLoaded", () => {
      const venueField = document.getElementById("u_venue");
      const attendField = document.getElementById("u_attend");
      const maxNote = document.getElementById("attend_max_note");

      const capacityMap = {
        "Auditorium": 250,
        "Cañao Hall": 150,
        "Conference Room": 20,
        "Gymnasium": 1200,
        "Laboratory Room": 100,
        "Lecture Room (Main)": 60,
        "Training Center": 40,

        "AVR": 45,
        "Crime Lab": 50,
        "Deftac": 50,
        "Lecture Room (Legarda)": 45,
        "Moot Court": 45,
      };

      let currentMax = 9999;

      venueField.addEventListener("change", () => {
        const selected = venueField.value;
        currentMax = capacityMap[selected] || 9999;
        attendField.max = currentMax;
        attendField.placeholder = `Max ${currentMax}`;
        attendField.title = `Maximum allowed for ${selected}: ${currentMax}`;
        if (maxNote) maxNote.textContent = `Max: ${currentMax}`;
      });

      attendField.addEventListener("input", () => {
        const value = parseInt(attendField.value, 10);
        if (value > currentMax) {
          alert(`This venue allows only up to ${currentMax} attendees.`);
          attendField.value = currentMax;
        }
      });
    });
  </script>

  <body>
    <nav class="navbar">
      <div class="logo">
        <img
          src="https://upload.wikimedia.org/wikipedia/commons/8/84/UC_Official_Logo.png"
          alt="UC Logo"
          style="height: 75px; width: auto"
        />
      </div>
      <ul class="nav-links" id="navLinks">
        <li><a href="home.html" class="active">Home</a></li>
        <li><a href="#">Inquire</a></li>
        <li><a href="reservation.html">Reservation</a></li>
      </ul>
    </nav>

    <form id="userForm" class="full-form">
<div style="text-align: center; margin-bottom: 1rem;">
  <h2>On Campus Clients Request Form</h2>
  <small
          style="
            display: block;
            margin-top: 0.25rem;
            color: #0f0f0f;
            font-size: 0.75rem;
            line-height: 1.2;
          "
        >
          NOTE: Submit two (2) copies of this form at least two (2) weeks prior
          to your event.
        </small>
</div>
<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem">
      <label>
        Name of Organization/Office/College:
        <input
          type="text"
          name="u_organization"
          data-target="organization"
          style="width: 97%"
        />
      </label>
      <fieldset>
  <legend>Type of Activity</legend>
  <div style="display: flex; gap: 2rem; align-items: center;">
    <label>
      <input
        type="checkbox"
        name="u_activity_type"
        value="academic"
        onclick="onlyOne(this, 'u_activity_type')"
        data-target="activity_type"
        data-value="academic"
      />
      Academic
    </label>

    <label>
      <input
        type="checkbox"
        name="u_activity_type"
        value="Non-Academic"
        onclick="onlyOne(this, 'u_activity_type')"
        data-target="activity_type"
        data-value="Non-Academic"
      />
      Non-Academic
    </label>

    <label>
      <input
        type="checkbox"
        name="u_activity_type"
        value="College/Institutional"
        onclick="onlyOne(this, 'u_activity_type')"
        data-target="activity_type"
        data-value="College/Institutional"
      />
      College/Institutional
    </label>
  </div>
</fieldset>

      <label>
        Title of Activity/Program:
        <input
          type="text"
          name="u_title"
          data-target="title"
          style="width: 97%"
        />
      </label>

      <label>
        Nature of Activity:
        <input
          type="text"
          name="u_nature"
          data-target="nature"
          style="width: 97%"
        />
      </label>

      <label>
        Activity Start Time:
        <input type="time" id="u_start_time" name="u_start_time" data-target="start-time" />
      </label>

      <label>
        Activity End Time:
        <input type="time" id="u_end_time" name="u_end_time" data-target="end-time" />
      </label>

      <label>
          Requested Venue / Room
          <select
            id="u_venue"
            name="u_venue"
            data-target="venue"
            required
            style="width: 100%"
          >
            <option value="">— Select —</option>

            <!-- Main Campus -->
            <optgroup label="Main Campus">
              <option value="Auditorium">Auditorium</option>
              <option value="Cañao Hall">Cañao Hall</option>
              <option value="Conference Room">Conference Room</option>
              <option value="Gymnasium">Gymnasium</option>
              <option value="Laboratory Room">Laboratory Room</option>
              <option value="Lecture Room (Main)">Lecture Room (Main)</option>
              <option value="Training Center">Training Center</option>
            </optgroup>

            <option disabled>──────────</option>

            <!-- Legarda -->
            <optgroup label="Legarda">
              <option value="AVR">AVR</option>
              <option value="Crime Lab">Crime Lab</option>
              <option value="Deftac">Deftac</option>
              <option value="Lecture Room (Legarda)">
                Lecture Room (Legarda)
              </option>
              <option value="Moot Court">Moot Court</option>
            </optgroup>
          </select>
        </label>

      <label
          >Day / Date of Event
          <input
            type="date"
            id="u_date"
            name="u_date"
            data-target="event_date"
            required
          />
        </label>
        
      <fieldset>
        <legend>Participants</legend>

        <label>
          <input
            type="checkbox"
            name="u_participants"
            value="participants"
            onclick="onlyOne(this, 'u_participants')"
            data-target="activity-type"
            data-value="participants"
          />
          Members / Office Staff Only </label
        ><br />

        <label>
          <input
            type="checkbox"
            name="u_participants"
            value="members_staff"
            onclick="onlyOne(this, 'u_participants')"
            data-target="activity-type"
            data-value="members_staff"
          />
          With other UC Students / Employees </label
        ><br />

        <label>
          <input
            type="checkbox"
            name="u_participants"
            value="other_employees"
            onclick="onlyOne(this, 'u_participants')"
            data-target="activity-type"
            data-value="other_employees"
          />
          Open to Public / Outsiders </label
        ><br /><br />

        <label>
          Estimated Total No. of Participants:
          <input
            type="number"
            id="u_attend"
            name="u_attend"
            data-target="attendees"
            min="1"
            required
            style="width: 100%"
          />
          <small id="attend_max_note" style="margin-left: 5px">Max: —</small>
        </label>

        <label>
        Remarks / Conditions:
        <textarea
          name="u_remarks"
          data-target="remarks"
          style="width: 100%; height: 300px; resize: vertical"
        ></textarea>
      </label>

      </fieldset>

      <fieldset>
  <legend>Checklist (Make sure to upload all of the required documents)</legend>

  <div style="display: flex; flex-direction: column; gap: 0.5rem;">

    <div style="display: flex; justify-content: space-between; align-items: center;">
      <label style="flex: 1;">
        <input
          type="checkbox"
          name="u_checklist"
          value="invitation"
          data-target="checklist_invitation"
          onclick="syncCheckboxToTarget(this)"
        />
        Copy of Letter of Invitation / MOA (outside partner)
      </label>
      <input type="file" style="margin-top: 0.25rem; text-align: center; width: 45%" name="upload_invitation" accept="application/pdf" />
    </div>

    <div style="display: flex; justify-content: space-between; align-items: center;">
      <label style="flex: 1;">
        <input
          type="checkbox"
          name="u_checklist"
          value="resume"
          data-target="checklist_resume"
          onclick="syncCheckboxToTarget(this)"
        />
        Speaker / Facilitator Resume
      </label>
      <input type="file" style="margin-top: 0.25rem; text-align: center; width: 45%" name="upload_resume" accept="application/pdf" />
    </div>

    <div style="display: flex; justify-content: space-between; align-items: center;">
      <label style="flex: 1;">
        <input
          type="checkbox"
          name="u_checklist"
          value="committee"
          data-target="checklist_committee"
          onclick="syncCheckboxToTarget(this)"
        />
        Organizing Committee
      </label>
      <input type="file" style="margin-top: 0.25rem; text-align: center; width: 45%" name="upload_committee" accept="application/pdf" />
    </div>

    <div style="display: flex; justify-content: space-between; align-items: center;">
      <label style="flex: 1;">
        <input
          type="checkbox"
          name="u_checklist"
          value="budget"
          data-target="checklist_budget"
          onclick="syncCheckboxToTarget(this)"
        />
        Budget Request Form
      </label>
      <input type="file" style="margin-top: 0.25rem; text-align: center; width: 45%" name="upload_budget" accept="application/pdf" />
    </div>

    <div style="display: flex; justify-content: space-between; align-items: center;">
      <label style="flex: 1;">
        <input
          type="checkbox"
          name="u_checklist"
          value="resolutions"
          data-target="checklist_resolutions"
          onclick="syncCheckboxToTarget(this)"
        />
        Copy of USC / CSC / Alternative Resolutions
      </label>
      <input type="file" style="margin-top: 0.25rem; text-align: center; width: 45%" name="upload_resolutions" accept="application/pdf" />
    </div>

    <div style="display: flex; justify-content: space-between; align-items: center;">
      <label style="flex: 1;">
        <input
          type="checkbox"
          name="u_checklist"
          value="program"
          data-target="checklist_program"
          onclick="syncCheckboxToTarget(this)"
        />
        Copy of Program / Schedule of Activities
      </label>
      <input type="file" style="margin-top: 0.25rem; text-align: center; width: 45%" name="upload_program" accept="application/pdf" />
    </div>

    <div style="display: flex; justify-content: space-between; align-items: center;">
      <label style="flex: 1;">
        <input
          type="checkbox"
          name="u_checklist"
          value="posters"
          data-target="checklist_posters"
          onclick="syncCheckboxToTarget(this)"
        />
        Copy of Promotional Material / Posters
      </label>
      <input type="file" style="margin-top: 0.25rem; text-align: center; width: 45%" name="upload_posters" accept="application/pdf" />
    </div>

    <div style="display: flex; justify-content: space-between; align-items: center;">
      <label style="flex: 1;">
        <input
          type="checkbox"
          name="u_checklist"
          value="excuse_letter"
          data-target="checklist_excuse_letter"
          onclick="syncCheckboxToTarget(this)"
        />
        Excuse Letter of Students
      </label>
      <input type="file" style="margin-top: 0.25rem; text-align: center; width: 45%" name="upload_excuse_letter" accept="application/pdf" />
    </div>

    <div style="display: flex; justify-content: space-between; align-items: center;">
      <label style="flex: 1;">
        <input
          type="checkbox"
          name="u_checklist"
          value="participants_list"
          data-target="checklist_participants_list"
          onclick="syncCheckboxToTarget(this)"
        />
        List of Participants
      </label>
      <input type="file" style="margin-top: 0.25rem; text-align: center; width: 45%" name="upload_participants_list" accept="application/pdf" />
    </div>

    <div style="display: flex; justify-content: space-between; align-items: center;">
      <label style="flex: 1;">
        <input
          type="checkbox"
          name="u_checklist"
          value="floor_plan"
          data-target="checklist_floor_plan"
          onclick="syncCheckboxToTarget(this)"
        />
        Venue Floor Plan / Physical Set-up
      </label>
      <input type="file" style="margin-top: 0.25rem; text-align: center; width: 45%" name="upload_floor_plan" accept="application/pdf" />
    </div>

    <div style="display: flex; justify-content: space-between; align-items: center;">
      <label style="flex: 1;">
        <input
          type="checkbox"
          name="u_checklist"
          value="curfew_form"
          data-target="checklist_curfew_form"
          onclick="syncCheckboxToTarget(this)"
        />
        Curfew Form (Prompted on submition if needed. Applies when booking holidays, Sundays, before 7:30 AM, or after 7:30 PM)
      </label>
    </div>

  </div>
</fieldset>


      <label>
        Source of Budget:
        <input
          type="text"
          name="u_budget_source"
          data-target="budget_source"
          style="width: 100%"
        />
      </label>

      <label>
        Estimated Total Budget:
        <input
          type="text"
          name="u_estimated_budget"
          data-target="estimated_budget"
          style="width: 100%"
        />
      </label>
<fieldset>
      <p><strong>In-Charge of Activity</strong></p>

      <label>
        Name:
        <input
          type="text"
          name="u_incharge_name"
          data-target="incharge_name"
          style="width: 100%"
        />
      </label>

      <label>
        Designation:
        <input
          type="text"
          name="u_incharge_designation"
          data-target="incharge_designation"
          style="width: 100%"
        />
      </label>

      <label>
        Contact No.:
        <input
          type="text"
          name="u_incharge_contact"
          data-target="incharge_contact"
          style="width: 100%"
        />
      </label>

      <label>
        Email Address:
        <input
          type="email"
          name="u_incharge_email"
          data-target="incharge_email"
          style="width: 100%"
        />
      </label>
</fieldset>
      <fieldset>
        <legend><strong>Technical Requirements</strong></legend>

        <label>
          <input
            type="checkbox"
            name="u_wl_mic"
            value="yes"
            data-toggle-target="u_wl_mic_qty"
            data-target="wl_mic_check"
            onclick="toggleDependentInput(this)"
          />
          <input
            type="text"
            id="u_wl_mic_qty"
            name="u_wl_mic_qty"
            data-target="wl_mic"
            disabled
            style="width: 10%"
          />
          Pcs of WL microphones </label
        ><br />

        <label>
          <input
            type="checkbox"
            name="u_wired_mic"
            value="yes"
            data-toggle-target="u_wired_mic_qty"
            data-target="wired_mic_check"
            onclick="toggleDependentInput(this)"
          />
          <input
            type="text"
            id="u_wired_mic_qty"
            name="u_wired_mic_qty"
            data-target="wired_mic"
            disabled
            style="width: 10%"
          />
          Pcs of wired microphones </label
        ><br />

        <label>
          <input
            type="checkbox"
            name="u_mic_stands"
            value="yes"
            data-toggle-target="u_mic_stands_qty"
            data-target="mic_stands_check"
            onclick="toggleDependentInput(this)"
          />
          <input
            type="text"
            id="u_mic_stands_qty"
            name="u_mic_stands_qty"
            data-target="mic_stands"
            disabled
            style="width: 10%"
          />
          Pcs of microphone stands </label
        ><br />

        <label
          ><input
            type="checkbox"
            name="u_laptop"
            value="yes"
            data-target="laptops"
          />
          Laptop</label
        ><br />
        <label
          ><input
            type="checkbox"
            name="u_projector"
            value="yes"
            data-target="projectors"
          />
          Projector</label
        ><br />
        <label
          ><input
            type="checkbox"
            name="u_pa_system"
            value="yes"
            data-target="pa_systems"
          />
          PA System</label
        ><br />
        <label
          ><input
            type="checkbox"
            name="u_va_mixer"
            value="yes"
            data-target="va_mixers"
          />
          VA mixer</label
        ><br />

        <label>
          <input
            type="checkbox"
            name="u_lights"
            value="yes"
            data-toggle-target="u_lights_qty"
            data-target="lights_check"
            onclick="toggleDependentInput(this)"
          />
          <input
            type="text"
            id="u_lights_qty"
            name="u_lights_qty"
            data-target="lights"
            disabled
            style="width: 10%"
          />
          Par lights </label
        ><br />

        <label
          ><input
            type="checkbox"
            name="u_moving_lights"
            value="yes"
            data-target="moving_lights"
          />
          Moving intelligent lights</label
        ><br />
        <label
          ><input
            type="checkbox"
            name="u_follow_spot"
            value="yes"
            data-target="follow_spots"
          />
          Follow Spot</label
        ><br />

        <label>
          <input
            type="checkbox"
            name="u_others_tech"
            value="yes"
            data-toggle-target="u_others_tech_desc"
            data-target="other_lights"
            onclick="toggleDependentInput(this)"
          />
          Others
        </label>
        <input
          type="text"
          id="u_others_tech_desc"
          name="u_others_tech_desc"
          data-target="others_tech"
          disabled
          style="width: 90%"
        /><br />
        <label>
          Other Requirements:<br />
          <textarea
            id="u_other_requirements_tech"
            name="u_other_requirements_tech"
            data-target="other_requirements_tech"
            style="width: 98%; height: 110px; resize: vertical"
          ></textarea>
        </label>

        <label>
          Remarks:<br />
          <textarea
            id="u_remarks_tech"
            name="u_remarks_tech"
            data-target="remarks_tech"
            style="width: 98%; height: 110px; resize: vertical"
          ></textarea>
        </label>
      </fieldset>

      <fieldset>
        <legend><strong>Property Requirements</strong></legend>

        <label
          ><input type="checkbox" name="u_podium" data-target="podium" />
          Podium</label
        ><br />
        <label
          ><input
            type="checkbox"
            name="u_philippine_flag"
            data-target="philippine_flag"
          />
          Philippine Flag</label
        ><br />
        <label
          ><input type="checkbox" name="u_uc_flag" data-target="uc_flag" /> UC
          Flag</label
        ><br />
        <label
          ><input type="checkbox" name="u_flagpoles" data-target="flagpoles" />
          Flagpoles</label
        ><br />

        <label>
          <input
            type="checkbox"
            name="u_long_table"
            data-toggle-target="u_long_table"
            data-target="long_table_check"
            onclick="toggleDependentInput(this)"
          />
          <input
            type="text"
            id="u_long_table"
            name="u_long_table_count"
            data-target="long_table"
            disabled
            style="width: 10%"
          />
          Pcs of long tables </label
        ><br />

        <label>
          <input
            type="checkbox"
            name="u_teachers_tables"
            data-toggle-target="u_teachers_tables"
            data-target="teachers_tables_check"
            onclick="toggleDependentInput(this)"
          />
          <input
            type="text"
            id="u_teachers_tables"
            name="u_teachers_tables_count"
            data-target="teachers_tables"
            disabled
            style="width: 10%"
          />
          Pcs of teachers tables </label
        ><br />

        <label>
          <input
            type="checkbox"
            name="u_platforms"
            data-toggle-target="u_platforms"
            data-target="platforms_check"
            onclick="toggleDependentInput(this)"
          />
          <input
            type="text"
            id="u_platforms"
            name="u_platforms_count"
            data-target="platforms"
            disabled
            style="width: 10%"
          />
          Pcs of platforms </label
        ><br />

        <label>
          <input
            type="checkbox"
            name="u_chairs"
            data-toggle-target="u_chairs"
            data-target="chairs_check"
            onclick="toggleDependentInput(this)"
          />
          <input
            type="text"
            id="u_chairs"
            name="u_chairs_count"
            data-target="chairs"
            disabled
            style="width: 10%"
          />
          Pcs of chairs </label
        ><br />

        <label>
          <input
            type="checkbox"
            name="u_trashbins"
            data-toggle-target="u_trashbins"
            data-target="trashbins_check"
            onclick="toggleDependentInput(this)"
          />
          <input
            type="text"
            id="u_trashbins"
            name="u_trashbins_count"
            data-target="trashbins"
            disabled
            style="width: 10%"
          />
          Pcs of trashbins </label
        ><br />

        <label>
          <input
            type="checkbox"
            name="u_other_property_check"
            data-toggle-target="u_others_req_property"
            data-target="other_property_check"
            onclick="toggleDependentInput(this)"
          />
          Others
        </label>
        <input
          type="text"
          id="u_others_req_property"
          name="u_others_req_property"
          data-target="others_req_property"
          disabled
          style="width: 90%"
        /><br />

        <label>
          Other Requirements:<br />
          <textarea
            id="u_other_requirements_property"
            name="u_other_requirements_property"
            data-target="other_requirements_property"
            style="width: 98%; height: 110px; resize: vertical"
          ></textarea>
        </label>

        <label>
          Remarks:<br />
          <textarea
            id="u_remarks_property"
            name="u_remarks_property"
            data-target="remarks_property"
            style="width: 98%; height: 110px; resize: vertical"
          ></textarea>
        </label>
      </fieldset>

      <fieldset>
        <legend><strong>Support Requirements</strong></legend>

        <label
          ><input
            type="checkbox"
            name="u_internet_connection"
            value="yes"
            data-target="internet_connection"
          />
          Internet Connection</label
        ><br />

        <label
          ><input
            type="checkbox"
            name="u_desktop_stations"
            value="yes"
            data-target="desktop_stations"
          />
          Desktop stations</label
        ><br />

        <label
          ><input
            type="checkbox"
            name="u_network_support"
            value="yes"
            data-target="network_support"
          />
          Internet network support</label
        ><br />

        <label
          ><input
            type="checkbox"
            name="u_web_fb_posting"
            value="yes"
            data-target="web_fb_posting"
          />
          Web/FB posting</label
        ><br />

        <label>
          <input
            type="checkbox"
            name="u_multimedia_check"
            value="yes"
            data-target="multimedia_check"
            data-toggle-target="u_multimedia"
            onclick="toggleDependentInput(this)"
          />
          Additional multimedia equipment
        </label>
        <input
          type="text"
          id="u_multimedia"
          name="u_multimedia"
          data-target="multimedia"
          disabled
          style="width: 90%"
        /><br />

        <label>
          <input
            type="checkbox"
            name="u_others_support_check"
            value="yes"
            data-target="others_support_check"
            data-toggle-target="u_others_support"
            onclick="toggleDependentInput(this)"
          />
          Others
        </label>
        <input
          type="text"
          id="u_others_support"
          name="u_others_support"
          data-target="others_support"
          disabled
          style="width: 90%"
        /><br /><br /><br />

        <label
          >Other Requirements:<br />
          <textarea
            id="u_other_requirements_support1"
            name="u_other_requirements_support1"
            data-target="other_requirements_support"
            style="width: 98%; height: 110px; resize: vertical"
          ></textarea></label
        ><br /><br />

        <label
          >Remarks:<br />
          <textarea
            id="u_remarks_support1"
            name="u_remarks_support1"
            data-target="remarks_support"
            style="width: 98%; height: 110px; resize: vertical"
          ></textarea>
        </label>
      </fieldset>
</div>
      <fieldset>
        <legend><strong>Support Requirements</strong></legend>

        <div style="display: grid; grid-template-columns: 1fr 1fr; align-items: start;">
  <label>
    <input
      type="checkbox"
      name="u_medical_first_aid"
      value="yes"
      data-target="medical_first_aid"
    />
    Medical/first-aid
  </label>

  <label>
    <input
      type="checkbox"
      name="u_guards_check"
      value="yes"
      data-toggle-target="u_guards"
      data-target="guards_check"
      onclick="toggleDependentInput(this)"
    />
    Security guards
    <input
      type="text"
      id="u_guards"
      name="u_guards"
      data-target="guards"
      disabled
      style="width: 30%; margin-left: 0.5rem"
    />
  </label>

  <label>
    <input
      type="checkbox"
      name="u_parking_check"
      value="yes"
      data-toggle-target="u_parking"
      data-target="parking_check"
      onclick="toggleDependentInput(this)"
    />
    Parking for
    <input
      type="text"
      id="u_parking"
      name="u_parking"
      data-target="parking"
      disabled
      style="width: 30%; margin-left: 0.5rem"
    />
    vehicles
  </label>

  <label>
    <input
      type="checkbox"
      name="u_transportation_check"
      value="yes"
      data-toggle-target="u_transportation"
      data-target="transportation_check"
      onclick="toggleDependentInput(this)"
    />
    Transportation:
    <input
      type="text"
      id="u_transportation"
      name="u_transportation"
      data-target="transportation"
      disabled
      style="width: 50%; margin-left: 0.5rem"
    />
  </label>

<label>
    <input
      type="checkbox"
      name="u_personnel_staff_check"
      value="yes"
      data-toggle-target="u_personnel_staff"
      data-target="personnel_staff_check"
      onclick="toggleDependentInput(this)"
    />
    Other personnel/staff<br />
    <input
      type="text"
      id="u_personnel_staff"
      name="u_personnel_staff"
      data-target="personnel_staff"
      disabled
      style="width: 100%"
    />
  </label>
  
</div>
<label style="display: flex; align-items: center; gap: 0.5rem;">
  <input
    type="checkbox"
    name="u_green_board_posting"
    value="yes"
    data-target="green_board_posting"
  />
  <span>Green board posting:</span>
  <input
    type="file"
    name="green_board"
    accept="application/pdf"
    style="width: 50%;"
  />
  </label>
        <p>(write text below, all subject to approval and editing)</p>
        <input
          type="text"
          id="u_posting"
          name="u_posting"
          data-target="posting"
          style="width: 97%; height: 60px"
        /><br /><br />

        <label>
          Other Requirements:<br />
          <textarea
            id="u_other_requirements_support2"
            name="u_other_requirements_support2"
            data-target="other_requirements_support2"
            style="width: 98%; height: 110px; resize: vertical"
          ></textarea></label
        ><br /><br />

        <label>
          Remarks:<br />
          <textarea
            id="u_remarks_support2"
            name="u_remarks_support2"
            data-target="remarks_support2"
            style="width: 98%; height: 110px; resize: vertical"
          ></textarea>
        </label>
      </fieldset>


      <button type="button" id="preparePrint">Submit &amp; Print</button>
    </form>

    <form
      class="form-container"
      id="combinedForm"
      onsubmit="event.preventDefault(); window.print();"
    >
      <!-- PAGE 1 -->
       <input type="hidden" id="curfew_required" name="curfew_required" value="No" />
      <!-- Header Section -->
      <div id="page1" class="form-page active">
        <div class="header-row">
          <img src="pending logo" alt="University of the Cordilleras Logo" />
          <div style="text-align: right">
            <strong>VICE PRESIDENT FOR ACADEMICS AND RESEARCH</strong><br />
            <span
              >___________________________________ <br />
              College
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp</span
            >
          </div>
        </div>

        <!-- Second Section -->
        <div class="form-title">
          STUDENT ACTIVITY REQUEST FORM FOR ON-CAMPUS
        </div>
        <table>
          <tr>
            <td rowspan="2" style="width: 50%">
              <strong class="small" style="font-size: 11px">
                This SARF is to be used by colleges, offices, recognized student
                organizations, student councils and faculty members for
                requesting approval of student activities in the campus. Submit
                SARF in Triplicate, two (2) weeks prior to start date of
                activity.
              </strong>
            </td>
            <td rowspan="2" style="width: 30%">
              <strong>Type of Activity</strong><br />
              <label
                ><input
                  type="checkbox"
                  name="activity_type"
                  value="academic"
                  onclick="onlyOne(this, 'audience_type')"
                />
                Academic</label
              ><br />
              <label
                ><input
                  type="checkbox"
                  name="activity_type"
                  value="Non-Academic"
                  onclick="onlyOne(this, 'audience_type')"
                />
                Non-Academic</label
              ><br />
              <label
                ><input
                  type="checkbox"
                  name="activity_type"
                  value="College/Institutional"
                  onclick="onlyOne(this, 'audience_type')"
                />
                College/Institutional</label
              >
            </td>
            <td style="width: 20%">
              <strong>SARF/Control No.:</strong><br />
              <input
                type="text"
                id="control"
                name="control"
                style="width: 97%"
              />
            </td>
          </tr>
          <tr>
            <td>
              Received:<br />
              <input
                type="date"
                id="received_date"
                name="received_date"
                style="width: 97%"
              />
            </td>
          </tr>
        </table>

        <!-- Third Section -->
        <div class="two-columns">
          <!-- Left Column -->
          <div>
            <table>
              <tr>
                <td>
                  <strong>Name of Organization/Office/College:</strong>
                  <input
                    type="text"
                    id="organization"
                    name="organization_name"
                    style="width: 99%"
                  />
                </td>
              </tr>
              <tr>
                <td>
                  <strong>Title of Activity/Program:</strong>
                  <input
                    type="text"
                    id="title"
                    name="title"
                    style="width: 99%"
                  />
                </td>
              </tr>
              <tr>
                <td>
                  <strong>Nature of Activity:</strong>
                  <input
                    type="text"
                    id="nature"
                    name="nature"
                    style="width: 99%"
                  />
                </td>
              </tr>
              <tr>
                <td><strong>Activity Date/Time:</strong></td>
              </tr>
              <tr>
                <td>
                  &nbsp;&nbsp;Start:
                  <input type="time" id="start-time" name="start-time" />
                </td>
              </tr>
              <tr>
                <td>
                  <strong>&nbsp;&nbsp;End:</strong>
                  <input type="time" id="end-time" name="end-time" />
                </td>
              </tr>
              <tr>
                <td>
                  <strong>Venue/Location:</strong>
                  <input
                    type="text"
                    id="location"
                    name="location"
                    style="width: 99%"
                  />
                </td>
              </tr>
            </table>

            <table>
              <tr>
                <td>
                  <strong>Participants:</strong><br />
                  <label
                    ><input
                      type="checkbox"
                      name="activity-type"
                      value="participants"
                      onclick="onlyOne(this)"
                    />
                    Members/Office Staff Only</label
                  ><br />
                  <label
                    ><input
                      type="checkbox"
                      name="activity-type"
                      value="members_staff"
                      onclick="onlyOne(this)"
                    />
                    With other UC Students/Employees</label
                  ><br />
                  <label
                    ><input
                      type="checkbox"
                      name="activity-type"
                      value="other_employees"
                      onclick="onlyOne(this)"
                    />
                    Open to Public/Outsiders</label
                  ><br />
                  <strong>Estimated Total No. of Participants:</strong>
                  <input
                    type="number"
                    id="est_participants"
                    name="est_participants"
                    min="0"
                    step="1"
                  />
                </td>
              </tr>
            </table>
          </div>

          <!-- Right Column -->
          <div>
            <table>
              <tr>
                <td><strong>Remarks/Conditions:</strong></td>
              </tr>
              <tr>
                <td>
                  <textarea
                  id="remarks"
                    name="remarks"
                    style="width: 98%; height: 100px; resize: vertical"
                  ></textarea>
                </td>
              </tr>
            </table>

            <table>
              <tr>
                <td>
                  <strong>Checklist (For Review Use Only)</strong><br />

                  <label>
                    <input type="checkbox" id="checklist_invitation" />
                    Copy of Letter of Invitation/MOA (if with outside partner) </label
                  ><br />

                  <label>
                    <input type="checkbox" id="checklist_resume" />
                    Speaker/Facilitator Resume </label
                  ><br />

                  <label>
                    <input type="checkbox" id="checklist_committee" />
                    Organizing Committee </label
                  ><br />

                  <label>
                    <input type="checkbox" id="checklist_budget" />
                    Budget Request Form </label
                  ><br />

                  <label>
                    <input type="checkbox" id="checklist_resolutions" />
                    Copy of USC/CSC/Alternative Resolutions </label
                  ><br />

                  <label>
                    <input type="checkbox" id="checklist_program" />
                    Copy of Program/Schedule of Activities </label
                  ><br />

                  <label>
                    <input type="checkbox" id="checklist_posters" />
                    Copy of Promotional Material/Posters </label
                  ><br />

                  <label>
                    <input type="checkbox" id="checklist_excuse_letter" />
                    Excuse Letter of Students </label
                  ><br />

                  <label>
                    <input type="checkbox" id="checklist_participants_list" />
                    List of Participants </label
                  ><br />

                  <label>
                    <input type="checkbox" id="checklist_floor_plan" />
                    Venue Floor Plan/Physical Set-up </label
                  ><br />

                  <label>
                    <input type="checkbox" id="checklist_curfew_form" />
                    Curfew Form (if applicable)
                  </label>
                </td>
              </tr>
            </table>
          </div>
        </div>

        <div class="two-columns">
          <!-- Left Column -->
          <div>
            <table>
              <tr>
                <td colspan="2">
                  <strong>Source of Budget:</strong>
                  <input
                    type="text"
                    id="budget_source"
                    name="budget_source"
                    style="width: 97%"
                  />
                </td>
              </tr>
              <tr>
                <td colspan="2">
                  <strong>Estimated Total Budget:</strong>
                  <input
                    type="text"
                    name="estimated_budget"
                    id="estimated_budget"
                    style="width: 97%"
                  />
                </td>
              </tr>
              <tr>
                <td
                  colspan="2"
                  class="section-title"
                  style="text-align: center"
                >
                  <strong>In-Charge of Activity</strong>
                </td>
              </tr>
              <tr>
                <td style="width: 30%"><strong>Name:</strong></td>
                <td>
                  <input type="text" name="incharge_name" style="width: 97%" />
                </td>
              </tr>
              <tr>
                <td><strong>Designation:</strong></td>
                <td>
                  <input
                    type="text"
                    name="incharge_designation"
                    id="incharge_designation"
                    style="width: 97%"
                  />
                </td>
              </tr>
              <tr>
                <td><strong>Contact No.:</strong></td>
                <td>
                  <input
                    type="text"
                    name="incharge_contact"
                    id="incharge_contact"
                    style="width: 97%"
                  />
                </td>
              </tr>
              <tr>
                <td><strong>Email Address:</strong></td>
                <td>
                  <input
                    type="email"
                    name="incharge_email"
                    id="incharge_email"
                    style="width: 97%"
                  />
                </td>
              </tr>

              <tr>
                <td colspan="2" style="height: 100px">
                  <div style="text-align: center">
                    <strong style="font-size: 13px">
                      Liability Waiver<br />
                      [To be accomplished by the Adviser/Coordinator]
                    </strong>
                  </div>
                  <p style="font-size: 12px; text-align: justify">
                    I certify that the undersigned will stay with the students
                    and participants for the duration of the aforementioned
                    activity. Further, I acknowledge that as an Adviser of the
                    organizing body, I am fully liable to any untoward events
                    that may arise during the conduct of the above activity. My
                    signature below indicates that I have read the policy stated
                    in this Liability Waiver Form, and that I agree to abide by
                    this policy.
                  </p>
                  <div style="height: 50x">
                    <strong>
                      <div
                        style="display: flex; justify-content: space-between"
                      >
                        <div style="text-align: center; width: 60%">
                          _____________________________<br />
                          Adviser/Coordinator
                        </div>
                        <div style="text-align: center; width: 35%">
                          ___________________<br />
                          Date
                        </div>
                      </div>
                    </strong>
                  </div>
                </td>
              </tr>
            </table>
          </div>

          <!-- Right Column -->
          <div style="border: 1px solid black; margin-bottom: 16px">
            <table style="width: 90%; border-collapse: collapse">
              <tr>
                <td
                  colspan="2"
                  class="section-title"
                  style="text-align: center; border: none"
                >
                  SIGNATURES: Printed Name and Signature, Date
                </td>
              </tr>

              <tr>
                <td style="height: 32px; border: none" colspan="2"></td>
              </tr>

              <tr>
                <td style="border: none">
                  <div>_________________________________</div>
                  <div>President / Organizer</div>
                </td>
                <td style="width: 35%; border: none">Date: ___________</td>
              </tr>

              <tr>
                <td style="height: 32px; border: none" colspan="2"></td>
              </tr>

              <tr>
                <td style="border: none">
                  <div>_________________________________</div>
                  <div>Adviser / Coordinator</div>
                </td>
                <td style="border: none">Date: ___________</td>
              </tr>

              <tr>
                <td style="height: 32px; border: none" colspan="2"></td>
              </tr>

              <tr>
                <td style="border: none">
                  <div>_________________________________</div>
                  <div>Academic Dean / Program Chair</div>
                </td>
                <td style="border: none">Date: ___________</td>
              </tr>

              <tr>
                <td style="height: 32px; border: none" colspan="2"></td>
              </tr>

              <tr>
                <td style="border: none">
                  <div>_________________________________</div>
                  <div>VP for Academics and Research</div>
                </td>
                <td style="border: none">Date: ___________</td>
              </tr>

              <tr>
                <td style="height: 32px; border: none" colspan="2"></td>
              </tr>

              <tr>
                <td style="border: none">
                  <div>_________________________________</div>
                  <div>Campus Facilities, Planning & Dev't Officer</div>
                </td>
                <td style="border: none">Date: ___________</td>
              </tr>

              <tr>
                <td style="height: 2px; border: none" colspan="2"></td>
              </tr>
            </table>
          </div>
        </div>

        <table>
          <td colspan="7" class="section-title" style="text-align: center">
            <strong>Amount of insurance per insured individual (PhP)</strong>
          </td>
          <tr>
            <th style="width: 12%">Description of Persons to be Covered</th>
            <th>Basic Life</th>
            <th>Accidental Death & Dismemberment</th>
            <th>Double Indemnity for Public Conveyance</th>
            <th>Accidental Medical Expense</th>
            <th>Daily Accident Hospital Benefit</th>
            <th>Unprovoked Murder & Assault</th>
          </tr>
          <tr>
            <th>All Eligible Students</th>
            <th>15,000</th>
            <th>150,000</th>
            <th>300,000</th>
            <th>50,000</th>
            <th>500</th>
            <th>150,000</th>
          </tr>
        </table>

        <!-- Pg 1 nav -->
        <!-- old next page -->
      </div>

      <!-- PAGE 2 -->
      <div id="page2" class="form-page">
        <div class="two-columns">
          <!-- Left Column -->
          <div>
            <table>
              <td colspan="2" class="section-title" style="text-align: center">
                <strong>For BMO Support (if applicable)</strong><br />
                <strong
                  >Venue: <input type="text" name="venue" style="width: 87%"
                /></strong>
              </td>
              <tr>
                <td style="width: 50%">
                  <p>Technical Requirements:</p>
                  <label
                    ><input type="checkbox" id="wl_mic_check" /><input
                      type="text"
                      id="wl_mic"
                      name="wl_mic_check"
                      style="width: 20%"
                    />
                    pcs of WL microphones</label
                  ><br />
                  <label
                    ><input type="checkbox" id="wired_mic_check" /><input
                      type="text"
                      id="wired_mic"
                      name="wired_mic_check"
                      style="width: 20%"
                    />
                    pcs of wired microphones</label
                  ><br />
                  <label
                    ><input type="checkbox" id="mic_stands_check" /><input
                      type="text"
                      id="mic_stands"
                      name="mic_stands_check"
                      style="width: 20%"
                    />
                    pcs of microphone stands</label
                  ><br />
                  <label><input type="checkbox" id="laptops" /> Laptop</label
                  ><br />
                  <label
                    ><input type="checkbox" id="projectors" /> Projector</label
                  ><br />
                  <label
                    ><input type="checkbox" id="pa_systems" /> PA System</label
                  ><br />
                  <label
                    ><input type="checkbox" id="va_mixers" /> VA mixer</label
                  ><br />
                  <label
                    ><input type="checkbox" id="lights_check" /><input
                      type="text"
                      id="lights"
                      name="lights_check"
                      style="width: 20%"
                    />
                    par lights</label
                  ><br />
                  <label
                    ><input type="checkbox" id="moving_lights" /> Moving
                    intelligent lights</label
                  ><br />
                  <label
                    ><input type="checkbox" id="follow_spots" /> Follow
                    Spot</label
                  ><br />
                  <label
                    ><input type="checkbox" id="other_lights" /> Others</label
                  ><input
                    type="text"
                    id="others_tech"
                    name="others_tech_check"
                    style="width: 97%"
                  /><br />
                </td>
                <td>
                  <strong>Other Requirements:</strong><br />
                  <textarea
                    id="other_requirements_tech"
                    name="other_requirements"
                    style="width: 98%; height: 110px; resize: vertical"
                  ></textarea
                  ><br /><br />
                  <strong>Remarks:</strong><br />
                  <textarea
                    id="remarks_tech"
                    name="remarks"
                    style="width: 98%; height: 110px; resize: vertical"
                  ></textarea
                  ><br />
                </td>
              </tr>
              <tr>
                <td colspan="2" style="text-align: center">
                  <div
                    style="
                      display: flex;
                      justify-content: space-between;
                      margin-bottom: 5px;
                    "
                  >
                    <div style="text-align: left; width: 60%">
                      <strong>Noted and Approved:</strong>
                    </div>
                    <div style="text-align: left; width: 40%">
                      <strong>Date:</strong>
                    </div>
                  </div>

                  <div style="display: flex; justify-content: space-between">
                    <div style="text-align: center; width: 45%">
                      <strong>___________________________</strong><br />
                      <strong>Head, LMO</strong>
                    </div>
                    <div style="text-align: center; width: 55%">
                      <strong>__________________</strong>
                    </div>
                  </div>
                </td>
              </tr>
            </table>
          </div>

          <!-- Right Column -->
          <div>
            <table>
              <td
                colspan="2"
                class="section-title"
                style="text-align: center; height: 55px"
              >
                <strong>For LMO Support (if applicable)</strong><br />
              </td>
              <tr>
                <td style="width: 50%">
                  <p>Property Requirements:</p>

                  <label><input type="checkbox" id="podium" /> Podium</label
                  ><br />
                  <label
                    ><input type="checkbox" id="philippine_flag" /> Philippine
                    Flag</label
                  ><br />
                  <label><input type="checkbox" id="uc_flag" /> UC Flag</label
                  ><br />
                  <label
                    ><input type="checkbox" id="flagpoles" /> Flagpoles</label
                  ><br />

                  <label>
                    <input type="checkbox" id="long_table_check" />
                    <input
                      type="text"
                      id="long_table"
                      name="long_table"
                      style="width: 20%"
                    />
                    Pcs of long tables </label
                  ><br />

                  <label>
                    <input type="checkbox" id="teachers_tables_check" />
                    <input
                      type="text"
                      id="teachers_tables"
                      name="teachers_tables"
                      style="width: 20%"
                    />
                    Pcs of teachers tables </label
                  ><br />

                  <label>
                    <input type="checkbox" id="platforms_check" />
                    <input
                      type="text"
                      id="platforms"
                      name="platforms"
                      style="width: 20%"
                    />
                    Pcs of platforms </label
                  ><br />

                  <label>
                    <input type="checkbox" id="chairs_check" />
                    <input
                      type="text"
                      id="chairs"
                      name="chairs"
                      style="width: 20%"
                    />
                    Pcs of chairs </label
                  ><br />

                  <label>
                    <input type="checkbox" id="trashbins_check" />
                    <input
                      type="text"
                      id="trashbins"
                      name="trashbins"
                      style="width: 20%"
                    />
                    Pcs of trashbins </label
                  ><br />

                  <label>
                    <input type="checkbox" id="other_property_check" />
                    Others
                  </label>
                  <input
                    type="text"
                    id="others_req_property"
                    name="others_req_property"
                    style="width: 87%"
                  /><br />
                </td>

                <td>
                  <strong>Other Requirements:</strong><br />
                  <textarea
                    id="other_requirements_property"
                    name="other_requirements_property"
                    style="width: 98%; height: 110px; resize: vertical"
                  ></textarea
                  ><br /><br />

                  <strong>Remarks:</strong><br />
                  <textarea
                    id="remarks_property"
                    name="remarks_property"
                    style="width: 98%; height: 110px; resize: vertical"
                  ></textarea
                  ><br />
                </td>
              </tr>
              <tr>
                <td colspan="2" style="text-align: center">
                  <div
                    style="
                      display: flex;
                      justify-content: space-between;
                      margin-bottom: 5px;
                    "
                  >
                    <div style="text-align: left; width: 60%">
                      <strong>Noted and Prroved:</strong>
                    </div>
                    <div style="text-align: left; width: 40%">
                      <strong>Date:</strong>
                    </div>
                  </div>

                  <div style="display: flex; justify-content: space-between">
                    <div style="text-align: center; width: 45%">
                      <strong>___________________________</strong><br />
                      <strong>Head, LMO</strong>
                    </div>
                    <div style="text-align: center; width: 55%">
                      <strong>_____________________</strong>
                    </div>
                  </div>
                </td>
              </tr>
            </table>
          </div>
        </div>

        <!-- Second Column -->
        <div class="two-columns">
          <!-- Left Column -->
          <div>
            <table>
              <td colspan="2" class="section-title" style="text-align: center">
                <strong>For ITSS Support (if applicable)</strong><br />
              </td>
              <tr>
                <td style="width: 50%">
                  <p>Support Requirements:</p>

                  <label
                    ><input type="checkbox" id="internet_connection" /> Internet
                    Connection</label
                  ><br />

                  <label
                    ><input type="checkbox" id="desktop_stations" /> Desktop
                    stations</label
                  ><br />

                  <label
                    ><input type="checkbox" id="network_support" /> Internet
                    network support</label
                  ><br />

                  <label
                    ><input type="checkbox" id="web_fb_posting" /> Web/FB
                    posting</label
                  ><br />

                  <p>
                    (attach material here or email it to
                    <em><EMAIL></em>
                    — all subject to approval and editing)
                  </p>

                  <label>
                    <input type="checkbox" id="multimedia_check" />
                    Additional multimedia equipment
                  </label>
                  <input
                    type="text"
                    id="multimedia"
                    name="multimedia"
                    style="width: 97%; height: 40px"
                  /><br />

                  <label>
                    <input type="checkbox" id="others_support_check" />
                    Others
                  </label>
                  <input
                    type="text"
                    id="others_support"
                    name="others_support"
                    style="width: 97%"
                  /><br /><br />
                </td>

                <td>
                  <strong>Other Requirements:</strong><br />
                  <textarea
                    id="other_requirements_support"
                    name="other_requirements_support"
                    style="width: 98%; height: 110px; resize: vertical"
                  ></textarea
                  ><br /><br />

                  <strong>Remarks:</strong><br />
                  <textarea
                    id="remarks_support"
                    name="remarks_support"
                    style="width: 98%; height: 110px; resize: vertical"
                  ></textarea
                  ><br />
                </td>
              </tr>

              <tr>
                <td colspan="2" style="text-align: center">
                  <div
                    style="
                      display: flex;
                      justify-content: space-between;
                      margin-bottom: 5px;
                    "
                  >
                    <div style="text-align: left; width: 60%">
                      <strong>Noted and Approved:</strong>
                    </div>
                    <div style="text-align: left; width: 40%">
                      <strong>Date:</strong>
                    </div>
                  </div>

                  <div style="display: flex; justify-content: space-between">
                    <div style="text-align: center; width: 45%">
                      <strong>___________________________</strong><br />
                      <strong>Director</strong>
                    </div>
                    <div style="text-align: center; width: 55%">
                      <strong>_____________________</strong>
                    </div>
                  </div>
                </td>
              </tr>
            </table>
          </div>

          <!-- Right Column -->
          <div>
            <table>
              <td colspan="2" class="section-title" style="text-align: center">
                <strong>For ADMINISTRATIVE Support (if applicable)</strong
                ><br />
              </td>
              <tr>
                <td style="width: 50%">
                  <p>Support Requirements:</p>

                  <label>
                    <input type="checkbox" id="medical_first_aid" />
                    Medical/first-aid </label
                  ><br />

                  <label>
                    <input type="checkbox" id="guards_check" />
                    <input
                      type="text"
                      id="guards"
                      name="guards"
                      style="width: 20%"
                    />
                    security guards </label
                  ><br />

                  <label>
                    <input type="checkbox" id="parking_check" />
                    Parking for
                    <input
                      type="text"
                      id="parking"
                      name="parking"
                      style="width: 20%"
                    />
                    vehicles </label
                  ><br />

                  <label>
                    <input type="checkbox" id="personnel_staff_check" />
                    Other personnel/staff<br />
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <input
                      type="text"
                      id="personnel_staff"
                      name="personnel_staff"
                      style="width: 87%"
                    /> </label
                  ><br />

                  <label>
                    <input type="checkbox" id="transportation_check" />
                    Transportation:
                    <input
                      type="text"
                      id="transportation"
                      name="transportation"
                      style="width: 49%"
                    /> </label
                  ><br />

                  <label>
                    <input type="checkbox" id="green_board_posting" />
                    Green board posting: </label
                  ><br />

                  <p>(write text below, all subject to approval and editing)</p>
                  <input
                    type="text"
                    id="posting"
                    name="posting"
                    style="width: 97%; height: 60px"
                  />
                </td>

                <td>
                  <strong>Other Requirements:</strong><br />
                  <textarea
                    id="other_requirements_support2"
                    name="other_requirements_support2"
                    style="width: 98%; height: 110px; resize: vertical"
                  ></textarea
                  ><br /><br />

                  <strong>Remarks:</strong><br />
                  <textarea
                    id="remarks_support2"
                    name="remarks_support2"
                    style="width: 98%; height: 110px; resize: vertical"
                  ></textarea
                  ><br />
                </td>
              </tr>

              <tr>
                <td colspan="2" style="text-align: center">
                  <div
                    style="
                      display: flex;
                      justify-content: space-between;
                      margin-bottom: 5px;
                    "
                  >
                    <div style="text-align: left; width: 60%">
                      <strong>Noted and Prroved:</strong>
                    </div>
                    <div style="text-align: left; width: 40%">
                      <strong>Date:</strong>
                    </div>
                  </div>

                  <div style="display: flex; justify-content: space-between">
                    <div style="text-align: center; width: 45%">
                      <strong>___________________________</strong><br />
                      <strong>VP for Admin and Acad Services</strong>
                    </div>
                    <div style="text-align: center; width: 55%">
                      <strong>_____________________</strong>
                    </div>
                  </div>
                </td>
              </tr>
            </table>
          </div>
        </div>

        <!-- Third Column -->
        <div>
          <!-- Left Column -->
          <div>
            <table>
              <td colspan="3" class="section-title" style="text-align: center">
                <strong>For FMO Use:</strong><br />
              </td>
              <tr>
                <td style="width: 35%">
                  <p>Applicable Charges: (if any)</p>
                  <label>
                    <input type="checkbox" /> Janitorial PhP
                    <input
                      type="text"
                      id="janitoring_charge"
                      name="janitoring"
                      style="width: 30%"
                    /> </label
                  ><br />

                  <label>
                    <input type="checkbox" /> Houselights PhP
                    <input
                      type="text"
                      id="houselights_charge"
                      name="houselights"
                      style="width: 30%"
                    /> </label
                  ><br />

                  <label>
                    <input type="checkbox" /> Technical PhP
                    <input
                      type="text"
                      id="technical_charge"
                      name="technical"
                      style="width: 30%"
                    /> </label
                  ><br />

                  <label>
                    <input type="checkbox" /> Water PhP
                    <input
                      type="text"
                      id="water_charge"
                      name="water"
                      style="width: 30%"
                    /> </label
                  ><br />

                  <label>
                    <input type="checkbox" /> Staff OT PhP
                    <input
                      type="text"
                      id="staff_ot_charge"
                      name="staff_ot"
                      style="width: 30%"
                    /> </label
                  ><br />

                  <label>
                    <input type="checkbox" /> Others PhP
                    <input
                      type="text"
                      id="others_applicable_charge"
                      name="others_applicable"
                      style="width: 30%"
                    /> </label
                  ><br /><br /><br />

                  <strong>
                    Total: PhP
                    <input
                      type="text"
                      id="total_charge"
                      name="total"
                      style="width: 30%"
                    /> </strong
                  ><br /><br />
                </td>
                <td style="width: 24%">
                  <strong>Other Requirements:</strong><br />
                  <textarea
                    name="remarks"
                    style="width: 98%; height: 110px; resize: vertical"
                  ></textarea
                  ><br /><br />
                  <strong>Remarks:</strong><br />
                  <textarea
                    name="remarks"
                    style="width: 98%; height: 110px; resize: vertical"
                  ></textarea
                  ><br />
                </td>
                <td style="width: 41%">
                  <div style="text-align: center">
                    <strong style="font-size: 14px"> Service Agreement </strong>
                  </div>
                  <p style="font-size: 14px">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;I have read and discussed with
                    the Director of FMO the policies and guidelines on the use
                    of UC facilies and accept personal responsibility to abide
                    by them. I understand that the use of UC facilities is
                    contigent upon the approval of the concerned officials. I
                    also understand that there may be additional charges as
                    enumerated here. <br />
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;By signing here, I acknowledge
                    responsibility for the care and condition of facilities and
                    equipment used during the activity.
                  </p>
                  <br /><br /><br />
                  <p style="text-align: center; margin-top: 20px">
                    <span
                      style="
                        display: inline-block;
                        text-align: center;
                        margin-right: 40px;
                      "
                    >
                      <strong>________________________________</strong><br />
                      In-charge of Activity<br />
                      (Printed Name and Signature)
                    </span>
                    <span style="display: inline-block; text-align: center">
                      <strong>________________</strong><br />
                      Date
                    </span>
                  </p>
                </td>
              </tr>
              <tr>
                <td colspan="3" style="text-align: center">
                  <strong>Approved and Reserved:</strong><br />

                  <div
                    style="
                      display: inline-block;
                      text-align: center;
                      margin-right: 40px;
                    "
                  >
                    <strong
                      >_____________________________________________</strong
                    ><br />
                    <strong>Campus Facilities, Planning & Dev't Officer</strong>
                  </div>
                  <div style="display: inline-block; text-align: center">
                    <strong>_________________________</strong><br />
                    <strong>Date</strong>
                  </div>
                </td>
              </tr>
            </table>
          </div>

          <!-- Pg nav -->
           <!-- old next page -->
        </div>
      </div>
    </form>

    <script>
      document.addEventListener("DOMContentLoaded", () => {
        /* For editing */
        const HOLIDAYS = [
          "2025-01-01",
          "2025-04-09",
          "2025-12-25",
        ]; /* sample holidates */
        const EARLIEST = "07:30";
        const LATEST = "19:30";
        const CURFEW_URL = "exemption-form.html";

        const btnPrint = document.getElementById("preparePrint");
        if (!btnPrint) {
          console.error("preparePrint button not found");
          return;
        }

        /* Time string to total minutes */
        const toMin = (t) => {
          const [h, m] = t.split(":").map(Number);
          return h * 60 + m;
        };

        /* Determine if curfew rules are triggered */
        function curfewReasons() {
          const date = document.getElementById("u_date")?.value;
          const start = document.getElementById("u_start_time")?.value;
          const end = document.getElementById("u_end_time")?.value;

          // Track reasoning
          const reasons = [];

          // uses from YYYY-MM-DD
          if (date) {
            const [year, month, day] = date.split("-").map(Number);
            const d = new Date(year, month - 1, day);

            const isSunday = d.getDay() === 0;
            if (isSunday) reasons.push("• Event is on a **Sunday**.");

            if (HOLIDAYS.includes(date)) {
              reasons.push("• Event is on a **holiday**.");
            }
          }

          // Exit early if no time inputs
          if (!start || !end) return reasons;

          if (toMin(start) < toMin(EARLIEST)) {
            reasons.push("• Starts **before 7:30-AM**.");
          }

          if (toMin(end) > toMin(LATEST)) {
            reasons.push("• Ends **after 7:30-PM**.");
          }



          return reasons;
        }

        /* Copy values from userform to internal form */
        function copyUserValues() {
          document
            .querySelectorAll(
              '#userForm [data-target]:not([type="checkbox"]):not([type="radio"])'
            )
            .forEach((src) => {
              const dst = document.getElementById(src.dataset.target);
              if (dst) {
                dst.value = src.value;
                dst.textContent = src.value;
              }
            });

          // checkboxes or radios
          document
            .querySelectorAll(
              '#userForm input[type="checkbox"][data-target], #userForm input[type="radio"][data-target]'
            )
            .forEach((src) => {
              if (!src.checked) return;

              const tgtName = src.dataset.target;
              const tgtValue = src.dataset.value;

              let dst =
                tgtValue !== undefined
                  ? document.querySelector(
                      `#combinedForm input[name="${tgtName}"][value="${tgtValue}"]`
                    )
                  : document.getElementById(tgtName);

              if (dst) dst.checked = true;
            });
        }

        /* Handle submit/print with curfew check */
        let curfewAcknowledged = false;

        btnPrint.addEventListener("click", () => {
          const reasons = curfewReasons();

          // If curfew rules are triggered and not yet acknowledged
          if (reasons.length && !curfewAcknowledged) {
            const msg =
              "This booking triggers curfew rules:\n\n" +
              reasons.join("\n") +
              "\n\nDo you want to open the Curfew/Exemption form now?";
            if (confirm(msg)) {
              window.open(CURFEW_URL, "_blank");
              curfewAcknowledged = true;

              // Let internal form know curfew form was needed
              const flag = document.getElementById("curfew_required");
              if (flag) flag.value = "Yes";
            }
            return; // Cancel actual print until confirmation next time
          }

          copyUserValues();
          window.print();
        });

        /* Update header info before printing */
        window.addEventListener("beforeprint", () => {
          const subDate = document.getElementById("submission-date");
          if (subDate) {
            subDate.textContent = new Date().toLocaleDateString("en-US", {
              year: "numeric",
              month: "long",
              day: "numeric",
            });
          }

          const ctrlIn = document.getElementById("control");
          const ctrlOut = document.getElementById("control-display");
          if (ctrlIn && ctrlOut) {
            ctrlOut.textContent = ctrlIn.value || "___________";
          }
        });
      });
    </script>
    <script>
  window.addEventListener("beforeprint", () => {
    const flag = document.getElementById("curfew_required");
    const exemptionDiv = document.getElementById("exemption-section");

    if (flag && flag.value === "Yes" && exemptionDiv) {
      exemptionDiv.style.display = "block";
    } else if (exemptionDiv) {
      exemptionDiv.style.display = "none";
    }
  });
</script>

    <footer class="footer">
      © 2025 University of the Cordilleras. All Rights Reserved.
    </footer>
  </body>
</html>