<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Request Forms - UC FMO Admin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="/utils/navbar.css">
    <link rel="stylesheet" href="/utils/modals.css">
    <link rel="stylesheet" href="/css/components/modern-filter.css">
    <link rel="stylesheet" href="/css/admin/request-forms.css">
</head>
<body>
    <!-- Navbar Container -->
    <div id="navbar-container"></div>

    <div class="container">
        <div class="page-header">
            <h1><i class="fas fa-file-alt"></i> Request Forms Management</h1>
            <p>Review and manage all user form submissions across the system</p>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-cards" id="statsCards">
            <div class="stat-card">
                <div class="stat-number" id="totalForms">0</div>
                <div class="stat-label">Total Forms</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="pendingForms">0</div>
                <div class="stat-label">Pending Review</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="acceptedForms">0</div>
                <div class="stat-label">Accepted</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="rejectedForms">0</div>
                <div class="stat-label">Rejected</div>
            </div>
        </div>

        <!-- Modern Filter Component -->
        <div id="modernFilter"></div>

        <div id="formsContainer">
            <div class="loading">
                <div class="loading-spinner"></div>
                <p>Loading forms...</p>
            </div>
        </div>

        <div id="pagination" class="pagination" style="display: none;"></div>
    </div>

    <footer class="footer">
        © 2025 University of the Cordilleras. All Rights Reserved.
    </footer>

    <!-- Modal Container -->
    <div id="modal-container"></div>

    <script>
        // Global variables
        let formsManager;
        let modernFilter;

        // Initialize the page
        function initializePage() {
            // Initialize Forms Manager for admin
            formsManager = new FormsManager({
                apiEndpoint: '/api/sarf/admin/all-forms',
                itemsPerPage: 4,
                isAdmin: true,
                containerId: 'formsContainer',
                paginationId: 'pagination',
                statsEnabled: true
            });

            // Make formsManager globally accessible for AdminFormManager
            window.formsManager = formsManager;

            // Initialize Modern Filter with search capability
            modernFilter = new ModernFilter('modernFilter', {
                showSearch: true,
                showStatus: true,
                showType: true,
                showDateRange: false,
                placeholder: 'Search by username or email...',
                onFilter: (filters) => {
                    formsManager.applyFilters(filters);
                },
                onClear: () => {
                    formsManager.applyFilters({});
                }
            });

            // Load initial data
            formsManager.updateStats();
            formsManager.loadForms();
        }

        // Admin-specific functions are now handled by AdminFormManager

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            const token = localStorage.getItem('token');
            if (!token) {
                window.location.href = '/login';
                return;
            }

            // Verify admin role and initialize navbar
            fetch('/api/auth/verify', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            })
            .then(response => response.json())
            .then(data => {
                if (!data.user) {
                    window.location.href = '/login';
                    return;
                }

                // Only allow admin users
                if (isAdminRole(data.user.role)) {
                    initializeNavbar('admin', 'request-forms', data.user.role);
                    initializePage();
                } else {
                    window.location.href = '/login';
                }
            })
            .catch(error => {
                window.location.href = '/login';
            });
        });


    </script>
    <script src="/js/roleUtils.js"></script>
    <script src="/utils/navbar.js"></script>
    <script src="/utils/modals.js"></script>
    <script src="/js/components/modern-filter.js"></script>
    <script src="/js/utils/forms-manager.js"></script>
    <script src="/js/admin/form-management.js"></script>
</body>
</html>
