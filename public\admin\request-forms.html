<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Request Forms - UC FMO Admin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="/utils/navbar.css">
    <link rel="stylesheet" href="/utils/modals.css">
    <link rel="stylesheet" href="/css/components/modern-filter.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        .page-header {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            border-left: 5px solid #2e7d32;
        }
        .page-header h1 {
            margin: 0;
            color: #2e7d32;
            font-size: 32px;
            font-weight: 700;
        }
        .page-header p {
            margin: 10px 0 0 0;
            color: #666;
            font-size: 16px;
        }
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.2s;
        }
        .stat-card:hover {
            transform: translateY(-2px);
        }
        .stat-card .stat-number {
            font-size: 28px;
            font-weight: bold;
            color: #2e7d32;
        }
        .stat-card .stat-label {
            color: #666;
            font-size: 14px;
            margin-top: 8px;
            font-weight: 500;
        }
        .filters {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 25px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            align-items: end;
        }
        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .filter-group label {
            font-weight: 600;
            color: #333;
            font-size: 14px;
        }
        .filter-group select, 
        .filter-group input {
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
            background: white;
        }
        .filter-group select:focus, 
        .filter-group input:focus {
            outline: none;
            border-color: #2e7d32;
        }
        .filter-actions {
            display: flex;
            gap: 10px;
        }
        .forms-grid {
            display: grid;
            gap: 20px;
        }
        .form-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 25px;
            transition: transform 0.2s;
            border-left: 5px solid #2e7d32;
        }
        .form-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        }
        .form-card.campus {
            border-left-color: #2e7d32;
        }
        .form-card.internal {
            border-left-color: #1976d2;
        }
        .form-card.external {
            border-left-color: #f57c00;
        }
        .form-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
        }
        .form-title {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }
        .form-type-badge {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
        }
        .form-type-badge.internal {
            background: #e3f2fd;
            color: #1976d2;
        }
        .form-type-badge.external {
            background: #fff3e0;
            color: #f57c00;
        }
        .form-details {
            margin-bottom: 20px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .form-detail {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        .form-detail strong {
            color: #555;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .form-detail span {
            color: #333;
            font-size: 14px;
        }
        .status-badge {
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            display: inline-block;
        }
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        .status-accepted {
            background: #d4edda;
            color: #155724;
        }
        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }
        .form-actions {
            display: flex;
            gap: 12px;
            margin-top: 20px;
            flex-wrap: wrap;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s;
        }
        .btn-primary {
            background: #2e7d32;
            color: white;
        }
        .btn-primary:hover {
            background: #1b5e20;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-success:hover {
            background: #218838;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .btn-secondary {
            background: #6c757d;
            color: rgb(1, 223, 30);
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .empty-state img {
            width: 120px;
            opacity: 0.5;
            margin-bottom: 20px;
        }
        .loading {
            text-align: center;
            padding: 40px;
        }
        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #2e7d32;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 30px;
        }
        .pagination button {
            padding: 10px 16px;
            border: 2px solid #e0e0e0;
            background: white;
            cursor: pointer;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s;
        }
        .pagination button:hover:not(:disabled) {
            background: #f8f9fa;
            border-color: #2e7d32;
        }
        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .pagination .current-page {
            background: #2e7d32;
            color: white;
            border-color: #2e7d32;
        }
        .footer {
            background-color: #033e2e;
            color: white;
            text-align: center;
            padding: 20px;
            font-size: 14px;
            margin-top: 50px;
        }
    </style>
</head>
<body>
    <!-- Navbar Container -->
    <div id="navbar-container"></div>

    <div class="container">
        <div class="page-header">
            <h1><i class="fas fa-file-alt"></i> Request Forms Management</h1>
            <p>Review and manage all user form submissions across the system</p>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-cards" id="statsCards">
            <div class="stat-card">
                <div class="stat-number" id="totalForms">0</div>
                <div class="stat-label">Total Forms</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="pendingForms">0</div>
                <div class="stat-label">Pending Review</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="acceptedForms">0</div>
                <div class="stat-label">Accepted</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="rejectedForms">0</div>
                <div class="stat-label">Rejected</div>
            </div>
        </div>

        <!-- Modern Filter Component -->
        <div id="modernFilter"></div>

        <div id="formsContainer">
            <div class="loading">
                <div class="loading-spinner"></div>
                <p>Loading forms...</p>
            </div>
        </div>

        <div id="pagination" class="pagination" style="display: none;"></div>
    </div>

    <footer class="footer">
        © 2025 University of the Cordilleras. All Rights Reserved.
    </footer>

    <!-- Modal Container -->
    <div id="modal-container"></div>

    <script>
        // Global variables
        let formsManager;
        let modernFilter;

        // Initialize the page
        function initializePage() {
            // Initialize Forms Manager for admin
            formsManager = new FormsManager({
                apiEndpoint: '/api/sarf/admin/all-forms',
                itemsPerPage: 4,
                isAdmin: true,
                containerId: 'formsContainer',
                paginationId: 'pagination',
                statsEnabled: true
            });

            // Initialize Modern Filter with search capability
            modernFilter = new ModernFilter('modernFilter', {
                showSearch: true,
                showStatus: true,
                showType: true,
                showDateRange: false,
                placeholder: 'Search by username or email...',
                onFilter: (filters) => {
                    formsManager.applyFilters(filters);
                },
                onClear: () => {
                    formsManager.applyFilters({});
                }
            });

            // Load initial data
            formsManager.updateStats();
            formsManager.loadForms();
        }

        // Admin-specific functions

        // Preview PDF
        function previewPDF(pdfUrl) {
            window.open(pdfUrl, '_blank');
        }

        // Download form PDF
        async function downloadFormPDF(formId, formType) {
            try {
                const token = localStorage.getItem('token');
                let downloadUrl = '';

                // Determine the correct download endpoint based on form type
                if (formType === 'campus') {
                    downloadUrl = `/api/sarf/download/${formId}`;
                } else if (formType === 'internal') {
                    downloadUrl = `/api/sarf/internal/download/${formId}`;
                } else if (formType === 'external') {
                    downloadUrl = `/api/sarf/external/download/${formId}`;
                }

                const response = await fetch(downloadUrl, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `${formType.toUpperCase()}-Form-${formId}.pdf`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                } else {
                    const error = await response.json();
                    await showErrorModal({
                        title: 'Download Failed',
                        message: error.message || 'Failed to download PDF'
                    });
                }
            } catch (error) {
                console.error('Error downloading PDF:', error);
                await showErrorModal({
                    title: 'Download Error',
                    message: 'Failed to download PDF. Please try again.'
                });
            }
        }

        // Update form status
        async function updateFormStatus(formId, status) {
            try {
                const token = localStorage.getItem('token');

                const response = await fetch(`/api/sarf/admin/update-status/${formId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({ status })
                });

                const result = await response.json();

                if (result.success) {
                    await showSuccessModal({
                        title: 'Status Updated',
                        message: `Form has been ${status} successfully.`,
                        onClose: () => {
                            // Reload forms and stats after modal is closed
                            try {
                                formsManager.updateStats();
                                formsManager.refresh();
                            } catch (error) {
                                console.error('Error refreshing forms after status update:', error);
                            }
                        }
                    });
                } else {
                    await showErrorModal({
                        title: 'Update Failed',
                        message: result.message || 'Failed to update form status'
                    });
                }
            } catch (error) {
                console.error('Error updating form status:', error);
                await showErrorModal({
                    title: 'Update Error',
                    message: 'Failed to update form status. Please try again.'
                });
            }
        }

        // Show reject modal with notes
        async function showRejectModal(formId) {
            return new Promise((resolve) => {
                const modalId = 'reject-modal-' + Date.now();

                const modalHTML = `
                    <div class="modal-overlay" id="${modalId}">
                        <div class="modal-content confirmation-modal">
                            <div class="modal-header danger">
                                <i class="fas fa-exclamation-triangle"></i>
                                <h3>Reject Form</h3>
                            </div>
                            <div class="modal-body">
                                <div style="margin-bottom: 15px;">
                                    Are you sure you want to reject this form? Please provide a reason:
                                </div>
                                <textarea id="rejectionNotes" placeholder="Enter rejection reason..."
                                          style="width: 100%; height: 100px; padding: 10px; border: 1px solid #ddd; border-radius: 4px; resize: vertical;"></textarea>
                            </div>
                            <div class="modal-footer">
                                <button class="modal-btn cancel-btn" data-action="cancel">
                                    <i class="fas fa-times"></i>
                                    Cancel
                                </button>
                                <button class="modal-btn confirm-btn danger" data-action="confirm">
                                    <i class="fas fa-check"></i>
                                    Reject Form
                                </button>
                            </div>
                        </div>
                    </div>
                `;

                document.getElementById('modal-container').innerHTML = modalHTML;
                const modal = document.getElementById(modalId);

                // Add event listeners
                modal.addEventListener('click', (e) => {
                    if (e.target.classList.contains('modal-overlay')) {
                        closeRejectModal(modalId, resolve);
                    }
                });

                modal.querySelectorAll('.modal-btn').forEach(btn => {
                    btn.addEventListener('click', async (e) => {
                        const action = e.currentTarget.getAttribute('data-action');

                        if (action === 'confirm') {
                            const rejectionNotesElement = document.getElementById('rejectionNotes');
                            const rejectionNotes = rejectionNotesElement ? rejectionNotesElement.value.trim() : '';

                            if (!rejectionNotes) {
                                await showErrorModal({
                                    title: 'Rejection Notes Required',
                                    message: 'Please provide a reason for rejecting this form.'
                                });
                                return;
                            }

                            closeRejectModal(modalId, resolve);
                            await processRejection(formId, rejectionNotes);
                        } else {
                            closeRejectModal(modalId, resolve);
                        }
                    });
                });

                // Show modal with animation
                setTimeout(() => modal.classList.add('show'), 10);
            });
        }

        function closeRejectModal(modalId, resolve) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hide');
                setTimeout(() => {
                    modal.remove();
                    resolve();
                }, 300);
            }
        }

        async function processRejection(formId, rejectionNotes) {
            try {
                const token = localStorage.getItem('token');

                const response = await fetch(`/api/sarf/admin/update-status/${formId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        status: 'rejected',
                        rejection_notes: rejectionNotes
                    })
                });

                const result = await response.json();

                if (result.success) {
                    await showSuccessModal({
                        title: 'Form Rejected',
                        message: 'Form has been rejected successfully.',
                        onClose: () => {
                            // Reload forms and stats after modal is closed
                            try {
                                formsManager.updateStats();
                                formsManager.refresh();
                            } catch (error) {
                                console.error('Error refreshing forms after rejection:', error);
                            }
                        }
                    });
                } else {
                    await showErrorModal({
                        title: 'Rejection Failed',
                        message: result.message || 'Failed to reject form'
                    });
                }
            } catch (error) {
                console.error('Error rejecting form:', error);
                await showErrorModal({
                    title: 'Rejection Error',
                    message: 'Failed to reject form. Please try again.'
                });
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            const token = localStorage.getItem('token');
            if (!token) {
                window.location.href = '/login';
                return;
            }

            // Verify admin role and initialize navbar
            fetch('/api/auth/verify', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            })
            .then(response => response.json())
            .then(data => {
                if (!data.user) {
                    window.location.href = '/login';
                    return;
                }

                // Only allow admin users
                if (isAdminRole(data.user.role)) {
                    initializeNavbar('admin', 'request-forms', data.user.role);
                    initializePage();
                } else {
                    window.location.href = '/login';
                }
            })
            .catch(error => {
                window.location.href = '/login';
            });
        });


    </script>
    <script src="/js/roleUtils.js"></script>
    <script src="/utils/navbar.js"></script>
    <script src="/utils/modals.js"></script>
    <script src="/js/components/modern-filter.js"></script>
    <script src="/js/utils/forms-manager.js"></script>
</body>
</html>
