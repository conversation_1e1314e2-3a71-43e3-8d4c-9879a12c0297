/* Modern Navbar Styles */
.navbar {
    background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%);
    color: white;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    position: sticky;
    top: 0;
    z-index: 1000;
    min-height: 70px;
    backdrop-filter: blur(10px);
}

/* Brand Section */
.navbar-brand {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    font-size: 1.2rem;
}

.navbar-logo {
    height: 55px;
    width: auto;
    border-radius: 8px;
}

.navbar-title {
    color: white;
    font-weight: 700;
    letter-spacing: 0.5px;
}

/* Navigation Links */
.navbar-nav {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 0.5rem;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.nav-link:hover::before {
    left: 100%;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.nav-link i {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

/* Dropdown Styles */
.dropdown {
    position: relative;
}

.dropdown-toggle {
    cursor: pointer;
}

.dropdown-arrow {
    font-size: 0.8rem;
    transition: transform 0.3s ease;
}

.dropdown.show .dropdown-arrow {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    min-width: 200px;
    z-index: 1001;
    list-style: none;
    padding: 8px 0;
    margin: 8px 0 0 0;
}

.dropdown.show .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-link {
    display: block;
    padding: 12px 20px;
    color: #333;
    text-decoration: none;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.dropdown-link:hover {
    background: #f8f9fa;
    color: #2e7d32;
    border-left-color: #2e7d32;
    padding-left: 24px;
}

/* User Section */
.navbar-user {
    display: flex;
    align-items: center;
    gap: 16px;
}

/* Public Navbar Buttons */
.nav-link-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.nav-link-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.nav-link-btn.active {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border-color: rgba(255, 255, 255, 0.5);
}

.signup-btn {
    background: rgba(76, 175, 80, 0.9);
    border-color: rgba(76, 175, 80, 1);
}

.signup-btn:hover {
    background: #4caf50;
    border-color: #4caf50;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

.user-info i {
    font-size: 1.1rem;
}

.logout-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(244, 67, 54, 0.9);
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.logout-btn:hover {
    background: #d32f2f;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
}

.logout-btn i {
    font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .navbar {
        padding: 0 1rem;
        flex-wrap: wrap;
        min-height: auto;
    }
    
    .navbar-nav {
        flex-direction: column;
        width: 100%;
        margin-top: 1rem;
        gap: 0.25rem;
    }
    
    .nav-link span {
        display: none;
    }
    
    .navbar-title {
        font-size: 1rem;
    }
    
    .navbar-logo {
        height: 35px;
    }
    
    .dropdown-menu {
        position: static;
        box-shadow: none;
        background: rgba(255, 255, 255, 0.1);
        margin: 0;
        border-radius: 4px;
    }
    
    .dropdown-link {
        color: rgba(255, 255, 255, 0.9);
        border-left: none;
        padding-left: 20px;
    }
    
    .dropdown-link:hover {
        background: rgba(255, 255, 255, 0.1);
        color: white;
        padding-left: 24px;
    }
}

@media (max-width: 480px) {
    .navbar {
        padding: 0.5rem;
    }
    
    .navbar-brand {
        gap: 8px;
    }
    
    .user-info span,
    .logout-btn span {
        display: none;
    }
    
    .navbar-user {
        gap: 8px;
    }
}

/* Animation for navbar load */
.navbar {
    animation: slideDown 0.5s ease-out;
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}
