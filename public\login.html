<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UC Campus - Login</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <link rel="stylesheet" href="/utils/navbar.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 50%, #a5d6a7 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(46,125,50,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            pointer-events: none;
            z-index: 0;
        }
    </style>
</head>
<body>

    <!-- Navbar Container -->
    <div id="navbar-container"></div>

    <style>
    nav {
      position: sticky;
      top: 0;
      z-index: 999;
      background-color: #2e7d32;
      color: #ffffff;
      padding: 15px 30px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

.logo img {
  height: 75px;
  width: auto;
}

.nav-links {
  list-style: none;
  display: flex;
  gap: 2rem;
}

.nav-links li a {
  text-decoration: none;
  color: white;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-links li a:hover {
  color: #a5d6a7; 
}
        
    /* Dropdown */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            background-color: white;
            min-width: 150px;
            box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.2);
            z-index: 1;
            border-radius: 4px;
        }

        .dropdown-content a {
            color: black;
            padding: 10px 15px;
            text-decoration: none;
            display: block;
            border-bottom: 1px solid #ddd;
        }

        .dropdown-content a:last-child {
            border-bottom: none;
        }

        .dropdown-content a:hover {
            background-color: #f1f1f1;
        }

    /* Toggle class for show/hide */
        .show {
            display: block;
        }
    </style>

    <script>
        function toggleDropdown() {
            var dropdownContent = document.querySelector(".dropdown-content");
            dropdownContent.classList.toggle("show");
        }
    </script>

    <!-- Main Content -->
    <div class="main-container">
        <div class="login-container">
            <div class="login-header">
                <div class="login-icon">
                    <i class="fas fa-user-circle"></i>
                </div>
                <h2>Welcome Back</h2>
                <p class="login-subtitle">Sign in to your UC Campus account</p>
            </div>

            <div id="general-error" class="error-message" style="display: none;"></div>

            <form id="loginForm" class="login-form">
                <div class="input-container">
                    <div class="input-wrapper">
                        <i class="fas fa-user input-icon"></i>
                        <input type="text" name="username" id="username" placeholder="Enter your username" required>
                    </div>
                    <div class="error-message" id="username-error"></div>
                </div>

                <div class="input-container">
                    <div class="input-wrapper">
                        <i class="fas fa-lock input-icon"></i>
                        <input type="password" name="password" id="password" placeholder="Enter your password" required>
                        <button type="button" class="password-toggle" onclick="togglePassword()">
                            <i class="fas fa-eye" id="password-eye"></i>
                        </button>
                    </div>
                    <div class="error-message" id="password-error"></div>
                </div>

                <div class="remember-forgot">
                    <label class="checkbox-container">
                        <input type="checkbox" name="remember_me">
                        <span class="checkmark"></span>
                        Remember me
                    </label>
                    <a href="/forgot-password" class="forgot-link">Forgot password?</a>
                </div>

                <button type="submit" class="login-btn">
                    <span class="btn-text">Sign In</span>
                    <i class="fas fa-arrow-right btn-icon"></i>
                </button>
            </form>

            <div class="divider">
                <span>or</span>
            </div>

            <div class="signup-link">
                <p>Don't have an account? <a href="/signup" class="signup-btn-link">Create Account</a></p>
            </div>
        </div>

        <!-- Decorative Elements -->
        <div class="floating-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
        </div>
    </div>

    <style>
        .main-container {
            flex-grow: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            position: relative;
            z-index: 1;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 50px 40px;
            border-radius: 24px;
            width: 100%;
            max-width: 450px;
            box-shadow:
                0 20px 40px rgba(46, 125, 50, 0.1),
                0 8px 16px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #2e7d32, #4caf50, #66bb6a);
            border-radius: 24px 24px 0 0;
        }

        .login-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .login-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #2e7d32, #4caf50);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            box-shadow: 0 8px 20px rgba(46, 125, 50, 0.3);
        }

        .login-icon i {
            font-size: 2.5rem;
            color: white;
        }

        .login-header h2 {
            font-size: 2.2rem;
            margin-bottom: 8px;
            color: #1b5e20;
            font-weight: 700;
        }

        .login-subtitle {
            color: #666;
            font-size: 1rem;
            font-weight: 400;
        }

        .login-form {
            margin-bottom: 30px;
        }

        .input-container {
            margin-bottom: 25px;
            position: relative;
        }

        .input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }

        .input-icon {
            position: absolute;
            left: 18px;
            color: #2e7d32;
            font-size: 1.1rem;
            z-index: 2;
        }

        .input-container input {
            width: 100%;
            padding: 18px 18px 18px 55px;
            border: 2px solid #e0e0e0;
            border-radius: 16px;
            font-size: 1rem;
            outline: none;
            transition: all 0.3s ease;
            background-color: #fafafa;
            font-weight: 500;
        }

        .input-container input:focus {
            border-color: #2e7d32;
            background-color: #fff;
            box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.1);
            transform: translateY(-2px);
        }

        .input-container input.error {
            border-color: #dc3545;
            background-color: #fff5f5;
        }

        .password-toggle {
            position: absolute;
            right: 18px;
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            font-size: 1.1rem;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .password-toggle:hover {
            color: #2e7d32;
            background-color: rgba(46, 125, 50, 0.1);
        }

        .error-message {
            color: #dc3545;
            font-size: 0.85rem;
            margin-top: 8px;
            padding: 8px 12px;
            background-color: #fff5f5;
            border: 1px solid #fecaca;
            border-radius: 8px;
            display: none;
        }

        .error-message.show {
            display: block;
        }

        .remember-forgot {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .checkbox-container {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-size: 0.9rem;
            color: #666;
            font-weight: 500;
        }

        .checkbox-container input {
            display: none;
        }

        .checkmark {
            width: 20px;
            height: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            margin-right: 10px;
            position: relative;
            transition: all 0.3s ease;
        }

        .checkbox-container input:checked + .checkmark {
            background-color: #2e7d32;
            border-color: #2e7d32;
        }

        .checkbox-container input:checked + .checkmark::after {
            content: '\f00c';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
        }

        .forgot-link {
            color: #2e7d32;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .forgot-link:hover {
            color: #1b5e20;
            text-decoration: underline;
        }

        .login-btn {
            width: 100%;
            padding: 18px 24px;
            background: linear-gradient(135deg, #2e7d32, #4caf50);
            color: white;
            border: none;
            border-radius: 16px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            box-shadow: 0 4px 15px rgba(46, 125, 50, 0.3);
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .login-btn:hover::before {
            left: 100%;
        }

        .login-btn:hover {
            background: linear-gradient(135deg, #1b5e20, #2e7d32);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(46, 125, 50, 0.4);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .btn-icon {
            transition: transform 0.3s ease;
        }

        .login-btn:hover .btn-icon {
            transform: translateX(5px);
        }

        .divider {
            text-align: center;
            margin: 30px 0;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, #e0e0e0, transparent);
        }

        .divider span {
            background: rgba(255, 255, 255, 0.95);
            padding: 0 20px;
            color: #666;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .signup-link {
            text-align: center;
        }

        .signup-link p {
            color: #666;
            font-size: 0.95rem;
        }

        .signup-btn-link {
            color: #2e7d32;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            padding: 2px 4px;
            border-radius: 4px;
        }

        .signup-btn-link:hover {
            color: #1b5e20;
            background-color: rgba(46, 125, 50, 0.1);
        }

        .floating-shapes {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            overflow: hidden;
        }

        .shape {
            position: absolute;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(46, 125, 50, 0.1), rgba(76, 175, 80, 0.05));
            animation: float 6s ease-in-out infinite;
        }

        .shape-1 {
            width: 100px;
            height: 100px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape-2 {
            width: 150px;
            height: 150px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }

        .shape-3 {
            width: 80px;
            height: 80px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 10px;
            }

            .login-container {
                padding: 40px 30px;
                max-width: 100%;
            }

            .login-header h2 {
                font-size: 1.8rem;
            }

            .login-icon {
                width: 60px;
                height: 60px;
            }

            .login-icon i {
                font-size: 2rem;
            }

            .floating-shapes {
                display: none;
            }
        }
    </style>

    <script src="/js/auth.js"></script>
    <script>
        // Password toggle functionality
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const passwordEye = document.getElementById('password-eye');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordEye.classList.remove('fa-eye');
                passwordEye.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                passwordEye.classList.remove('fa-eye-slash');
                passwordEye.classList.add('fa-eye');
            }
        }

        // Enhanced form validation
        function validateForm() {
            const username = document.getElementById('username');
            const password = document.getElementById('password');
            let isValid = true;

            // Clear previous errors
            document.querySelectorAll('.error-message').forEach(error => {
                error.style.display = 'none';
                error.classList.remove('show');
            });
            document.querySelectorAll('input').forEach(input => {
                input.classList.remove('error');
            });

            // Username validation
            if (!username.value.trim()) {
                showError('username-error', 'Username is required');
                username.classList.add('error');
                isValid = false;
            } else if (username.value.trim().length < 3) {
                showError('username-error', 'Username must be at least 3 characters');
                username.classList.add('error');
                isValid = false;
            }

            // Password validation
            if (!password.value.trim()) {
                showError('password-error', 'Password is required');
                password.classList.add('error');
                isValid = false;
            }

            return isValid;
        }



        function showError(elementId, message) {
            const errorElement = document.getElementById(elementId);
            errorElement.textContent = message;
            errorElement.style.display = 'block';
            errorElement.classList.add('show');
        }

        document.getElementById('loginForm').addEventListener('submit', async function(event) {
            event.preventDefault();

            if (!validateForm()) {
                return;
            }

            const submitBtn = document.querySelector('.login-btn');
            const btnText = document.querySelector('.btn-text');
            const btnIcon = document.querySelector('.btn-icon');

            submitBtn.disabled = true;
            btnText.textContent = 'Signing In...';
            btnIcon.classList.remove('fa-arrow-right');
            btnIcon.classList.add('fa-spinner', 'fa-spin');

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();

                if (response.ok) {
                    localStorage.setItem('token', data.token);

                    btnText.textContent = 'Success!';
                    btnIcon.classList.remove('fa-spinner', 'fa-spin');
                    btnIcon.classList.add('fa-check');
                    submitBtn.style.background = 'linear-gradient(135deg, #28a745, #4caf50)';

                    setTimeout(() => {
                        if (isAdminRole(data.user.role)) {
                            window.location.href = '/admin/admin';
                        } else {
                            window.location.href = '/home';
                        }
                    }, 1000);
                } else {
                    throw new Error(data.message || 'Login failed');
                }
            } catch (error) {
                submitBtn.disabled = false;
                btnText.textContent = 'Sign In';
                btnIcon.classList.remove('fa-spinner', 'fa-spin', 'fa-check');
                btnIcon.classList.add('fa-arrow-right');
                submitBtn.style.background = '';

                const errorDiv = document.getElementById('general-error');
                errorDiv.textContent = error.message || 'An error occurred during login';
                errorDiv.style.display = 'block';
                errorDiv.classList.add('show');
            }
        });

        document.addEventListener('DOMContentLoaded', function() {
            initializeNavbar('public', 'login');
        });
    </script>
    <script src="/js/roleUtils.js"></script>
    <script src="/utils/navbar.js"></script>
</body>
</html>