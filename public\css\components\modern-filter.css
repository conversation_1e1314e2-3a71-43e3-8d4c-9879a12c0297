/* Modern Filter Component Styles */
.modern-filter-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    overflow: hidden;
    transition: all 0.3s ease;
    
}

.modern-filter-container:hover {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}



.filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.filter-title {
    display: flex;
    align-items: center;
    gap: 12px;
    color: white;
    font-size: 18px;
    font-weight: 600;
}

.filter-title i {
    font-size: 20px;
}

.filter-toggle {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-toggle:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.filter-toggle i {
    transition: transform 0.3s ease;
}

.filter-content {
    padding: 25px;
    background: white;
    transition: all 0.3s ease;
}

.filter-content.collapsed {
    max-height: 0;
    padding: 0 25px;
    overflow: hidden;
}

.filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.filter-row:last-child {
    margin-bottom: 0;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-group.search-group {
    grid-column: 1 / -1;
}

.filter-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #333;
    font-size: 14px;
    margin-bottom: 5px;
}

.filter-label i {
    color: #667eea;
    width: 16px;
}

/* Search Input Styles */
.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input {
    width: 100%;
    padding: 15px 50px 15px 45px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 16px;
    padding-left: 36px;
    background: #f8f9fa;
    transition: all 0.3s ease;
    outline: none;
}

.search-input:focus {
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-icon {
    position: absolute;
    left: 12px;
    color: #6c757d;
    font-size: 16px;
    z-index: 2;
}

.clear-search {
    position: absolute;
    right: 15px;
    background: #dc3545;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.clear-search:hover {
    background: #c82333;
    transform: scale(1.1);
}

/* Select Styles */
.select-wrapper {
    position: relative;
}

.modern-select {
    width: 100%;
    padding: 12px 40px 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 14px;
    background: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s ease;
    outline: none;
    appearance: none;
}

.modern-select:focus {
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.select-arrow {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    pointer-events: none;
    font-size: 12px;
}

/* Date Range Styles */
.date-range-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
}

.date-input {
    flex: 1;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 14px;
    background: #f8f9fa;
    transition: all 0.3s ease;
    outline: none;
}

.date-input:focus {
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.date-separator {
    color: #6c757d;
    font-weight: 500;
    white-space: nowrap;
}

/* Filter Actions */
.filter-actions {
    display: flex;
    align-items: center;
    gap: 15px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.apply-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.apply-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.clear-btn {
    background: #f8f9fa;
    color: #6c757d;
    border: 2px solid #e9ecef;
}

.clear-btn:hover {
    background: #e9ecef;
    color: #495057;
    transform: translateY(-1px);
}

.filter-results {
    margin-left: auto;
    display: flex;
    align-items: center;
}

.results-text {
    font-size: 14px;
    color: #6c757d;
    font-weight: 500;
    padding: 8px 16px;
    background: #f8f9fa;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.results-text.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .filter-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .filter-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-results {
        margin-left: 0;
        justify-content: center;
    }
    
    .filter-btn {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .modern-filter-container {
        border-radius: 12px;
    }
    
    .filter-header {
        padding: 15px 20px;
    }
    
    .filter-content {
        padding: 20px;
    }
    
    .search-input {
        padding: 12px 40px 12px 35px;
        font-size: 14px;
    }
}

/* Enhanced Form Cards and States */
.forms-grid {
    display: grid;
    gap: 20px;
    margin-top: 20px;
}

.form-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    padding: 25px;
    transition: all 0.3s ease;
    border-left: 5px solid #2e7d32;
    position: relative;
    overflow: hidden;
}

.form-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.form-card:hover::before {
    transform: scaleX(1);
}

.form-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
}

.form-card.campus {
    border-left-color: #2e7d32;
}

.form-card.internal {
    border-left-color: #1976d2;
}

.form-card.external {
    border-left-color: #f57c00;
}

.form-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
}

.form-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.form-type-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-type-badge.internal {
    background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
}

.form-type-badge.external {
    background: linear-gradient(135deg, #f57c00 0%, #ffb74d 100%);
}

.form-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.form-detail {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.form-detail strong {
    color: #666;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
}

.form-detail span {
    color: #333;
    font-size: 14px;
    font-weight: 500;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    width: fit-content;
}

.status-pending {
    background: linear-gradient(135deg, #ffc107 0%, #ffeb3b 100%);
    color: #856404;
}

.status-accepted {
    background: linear-gradient(135deg, #28a745 0%, #4caf50 100%);
    color: white;
}

.status-rejected {
    background: linear-gradient(135deg, #dc3545 0%, #f44336 100%);
    color: white;
}

.role-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.role-badge.admin {
    background: #e3f2fd;
    color: #1976d2;
}

.role-badge.student {
    background: #e8f5e8;
    color: #2e7d32;
}

.role-badge.external {
    background: #fff3e0;
    color: #f57c00;
}

.rejection-notes {
    grid-column: 1 / -1;
    padding: 15px;
    background: #fff5f5;
    border-radius: 8px;
    border-left: 4px solid #dc3545;
}

.rejection-text {
    color: #dc3545 !important;
    font-style: italic;
}

.form-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #f8f9fa;
    color: rgb(1, 223, 30);
    border: 2px solid #e9ecef;
}

.btn-secondary:hover {
    background: #e9ecef;
    color: #495057;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #4caf50 100%);
    color: white;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #f44336 100%);
    color: white;
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #ffeb3b 100%);
    color: #856404;
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
}

/* Loading and Error States */
.loading-state, .error-state, .empty-state {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.loading-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.empty-icon, .error-icon {
    font-size: 64px;
    color: #e9ecef;
    margin-bottom: 20px;
}

.error-icon {
    color: #dc3545;
}

/* Pagination Styles */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 30px;
    flex-wrap: wrap;
}

.pagination-btn {
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    background: white;
    cursor: pointer;
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
    color: #6c757d;
}

.pagination-btn:hover:not(:disabled):not(.disabled) {
    background: #f8f9fa;
    border-color: #667eea;
    color: #667eea;
    transform: translateY(-1px);
}

.pagination-btn:disabled,
.pagination-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.pagination-btn.current-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: #667eea;
}

.pagination-btn.current-page:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}
