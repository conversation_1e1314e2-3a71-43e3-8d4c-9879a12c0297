<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>UC Campus - Legarda Campus</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
  <link rel="stylesheet" href="/utils/navbar.css">
</head>
<body>

  <!-- Navbar Container -->
  <div id="navbar-container"></div>

  <main class="home">
    <section class="main">
      <h1>Legarda Campus</h1>
      <p>Legarda Rd, Baguio City</p>
    </section>
    <section class="main-image">
      <img src="https://upload.wikimedia.org/wikipedia/en/f/fd/UC-BCF_Legarda.jpg" alt="Legarda Image">
    </section>
  </main>

  <main class="home">
  <h1>Select Legarda Campus Facility</h1>
  <div class="reservation-options">
    <div class="option-card" onclick="openModal('AVR')">AVR</div>
    <div class="option-card" onclick="openModal('Crime Lab 1')">Crime Lab </div>
    <div class="option-card" onclick="openModal('Deftac')">Deftac</div>
    <div class="option-card" onclick="openModal('Lecture')">Lecture</div>
    <div class="option-card" onclick="openModal('Moot Court')">Moot Court</div>
  </div>

    <!-- Modal -->
  <div id="facilityModal" class="modal">
    <div class="modal-content">
      <span class="close" onclick="closeModal()">&times;</span>

   <!-- Facility Image -->
    <img id="facilityImage" src="" alt="Facility Image" class="modal-image" />

      <h2 id="facilityTitle">Facility Name</h2>
      <p><strong>Floor:</strong> <span id="floor"></span></p>
      <p><strong>Rooms:</strong> <span id="room"></span></p>
      <p><strong>Max Capacity:</strong> <span id="capacity"></span></p>
    </div>
  </div>
</main>

<script>
  const facilityData = {
    "AVR": {
      image: "https://3.imimg.com/data3/BD/VT/MY-14504247/audio-visual-room-500x500.png",
      floor: "2nd Floor",
      room: "L43",
      capacity: "45",
    },
    "Crime Lab": {
      image: "https://crimelabdesign.com/wp-content/uploads/2019/02/Title-5.jpg",
      floor: "2nd, 3rd Floor",
      room: "L35, L50, L52",
      capacity: "45",
    },
    "Deftac": {
      image: "https://yolobjj.wordpress.com/wp-content/uploads/2016/04/img_0866.jpg",
      floor: "2nd Basemeny",
      room: "L13",
      capacity: "45-50",
    },
    "Lecture": {
      image: "https://live.staticflickr.com/8546/8749462352_8ec0e14bae_h.jpg",
      floor: "1st, 3rd Floor",
      room: "L34, L51, L54, L55",
      capacity: "45",
    },
    "Moot Court": {
      image: "https://www.bennett.edu.in/wp-content/uploads/2023/04/Moot-Court.png",
      floor: "2nd Basement",
      room: "L12",
      capacity: "45",
    },
  };

  function openModal(facility) {
    const data = facilityData[facility];
    document.getElementById("facilityTitle").innerText = facility;
    document.getElementById("facilityImage").src = data.image;
    document.getElementById("floor").innerText = data.floor;
    document.getElementById("room").innerText = data.room;
    document.getElementById("capacity").innerText = data.capacity;
    document.getElementById("facilityModal").style.display = "block";
  }

  function closeModal() {
    document.getElementById("facilityModal").style.display = "none";
  }
</script>


  <footer class="footer">
  © 2025 University of the Cordilleras. All Rights Reserved.
</footer>

  <style>
    * {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', sans-serif;
  background-color: #f0f4f3;
}

nav {
      position: sticky;
      top: 0;
      z-index: 999;
      background-color: #2e7d32;
      color: #ffffff;
      padding: 15px 30px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

.nav-links {
  list-style: none;
  display: flex;
  gap: 2rem;
}

.nav-links li a {
  text-decoration: none;
  color: white;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-links li a:hover {
  color: #a5d6a7; 
}

.logo img {
  height: 75px;
  width: auto;
}

.home {
  text-align: center;
  padding: 3rem 2rem;
  background-color: #e8f5e9;
}

.main h1 {
  font-size: 2.5rem;
  color: #1b5e20;
  margin-bottom: .5rem;
}

.main p {
  font-size: 1.2rem;
  color: #4e944f;
  margin-bottom: 1rem;
}

h1 {
  font-size: 2.5rem;
  margin-bottom: 40px;
  color: #256d1b;
}

.reservation-options {
  display: flex;
  flex-direction: row; 
  align-items: center;
  gap: 30px;
}

.option-card {
  background-color: #ffffff;
  padding: 40px 30px;
  width: 250px;
  border-radius: 12px;
  text-decoration: none;
  color: #1b5e20;
  font-weight: 600;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #c5e1a5;
}

.option-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 24px rgba(67, 160, 71, 0.2);
  background-color: #e8f5e9;
  color: #2e7d32;
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 99;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 64, 0, 0.6);
  backdrop-filter: blur(3px);
}

.modal-content {
  background-color: #f1f8e9;
  margin: 10% auto;
  padding: 20px;
  border: 1px solid #c5e1a5;
  width: 80%;
  max-width: 600px;
  border-radius: 12px;
  color: #2e7d32;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.close {
  color: #1b5e20;
  float: right;
  font-size: 24px;
  font-weight: bold;
  cursor: pointer;
}

.modal-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 20px;
}

.modal-info {
  display: grid;
  grid-template-columns: 1fr;
  gap: 10px;
  font-size: 15px;
  color: #333;
}

.modal-info strong {
  color: #1b5e20;
}


.footer {
  background-color: #033E2E;
  color: white;
  text-align: center;
  padding: 20px;
  font-size: 14px;
}
  </style>

  <script>
    // Initialize navbar and check authentication
    document.addEventListener('DOMContentLoaded', function() {
      const token = localStorage.getItem('token');
      if (!token) {
        window.location.href = '/login';
        return;
      }

      // Verify user role and initialize navbar
      fetch('/api/auth/verify', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      .then(response => response.json())
      .then(data => {
        if (!data.user) {
          window.location.href = '/login';
          return;
        }

        if (isAdminRole(data.user.role)) {
          initializeNavbar('admin', 'campus', data.user.role);
        } else if (data.user.role === 'student' || data.user.role === 'external') {
          initializeNavbar('user', 'campus', data.user.role);
        } else {
          window.location.href = '/login';
        }
      })
      .catch(error => {
        window.location.href = '/login';
      });
    });
  </script>
  <script src="/js/roleUtils.js"></script>
  <script src="/utils/navbar.js"></script>
</body>
</html>