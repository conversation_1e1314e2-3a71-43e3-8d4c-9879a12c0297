/* Reusable Modal System Styles */

/* Modal Container */
#modal-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    pointer-events: none;
}

/* Modal Overlay */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    pointer-events: auto;
    backdrop-filter: blur(4px);
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal-overlay.hide {
    opacity: 0;
    visibility: hidden;
}

/* Modal Content */
.modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.7) translateY(-50px);
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.modal-overlay.show .modal-content {
    transform: scale(1) translateY(0);
}

.modal-overlay.hide .modal-content {
    transform: scale(0.7) translateY(-50px);
}

/* Modal Header */
.modal-header {
    padding: 25px 30px 20px;
    text-align: center;
    border-bottom: 1px solid #e0e0e0;
    position: relative;
}

.modal-header h3 {
    margin: 10px 0 0 0;
    font-size: 1.4rem;
    font-weight: 600;
    color: #333;
}

.modal-header i {
    font-size: 2.5rem;
    margin-bottom: 10px;
    display: block;
}

/* Success Icon Animation */
.success-icon {
    position: relative;
    display: inline-block;
}

.success-icon i {
    animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Modal Body */
.modal-body {
    padding: 25px 30px;
    text-align: center;
}

.modal-body p {
    margin: 0;
    font-size: 1.1rem;
    line-height: 1.6;
    color: #555;
}

/* Modal Footer */
.modal-footer {
    padding: 20px 30px 30px;
    display: flex;
    justify-content: center;
    gap: 15px;
}

/* Modal Buttons */
.modal-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
    justify-content: center;
}

.modal-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.modal-btn:active {
    transform: translateY(0);
}

/* Button Variants */
.cancel-btn {
    background: #6c757d;
    color: white;
}

.cancel-btn:hover {
    background: #5a6268;
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.3);
}

.confirm-btn.danger {
    background: #dc3545;
    color: white;
}

.confirm-btn.danger:hover {
    background: #c82333;
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.3);
}

.confirm-btn.warning {
    background: #ffc107;
    color: #212529;
}

.confirm-btn.warning:hover {
    background: #e0a800;
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.3);
}

.confirm-btn.info {
    background: #17a2b8;
    color: white;
}

.confirm-btn.info:hover {
    background: #138496;
    box-shadow: 0 6px 20px rgba(23, 162, 184, 0.3);
}

.confirm-btn.success,
.success-btn {
    background: #28a745;
    color: white;
}

.confirm-btn.success:hover,
.success-btn:hover {
    background: #218838;
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3);
}

.info-btn {
    background: #17a2b8;
    color: white;
}

.info-btn:hover {
    background: #138496;
    box-shadow: 0 6px 20px rgba(23, 162, 184, 0.3);
}

.secondary-btn {
    background: #6c757d;
    color: white;
}

.secondary-btn:hover {
    background: #5a6268;
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.3);
}

/* PDF Actions Container */
.pdf-actions {
    display: flex;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
    margin: 20px 0;
}

.pdf-actions .modal-btn {
    min-width: 140px;
}

.danger-btn {
    background: #dc3545;
    color: white;
}

.danger-btn:hover {
    background: #c82333;
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.3);
}

/* Header Color Variants */
.modal-header.danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}

.modal-header.danger h3 {
    color: white;
}

.modal-header.warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
}

.modal-header.warning h3 {
    color: #212529;
}

.modal-header.info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

.modal-header.info h3 {
    color: white;
}

.modal-header.success {
    background: linear-gradient(135deg, #28a745, #218838);
    color: white;
}

.modal-header.success h3 {
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 20px;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding-left: 20px;
        padding-right: 20px;
    }
    
    .modal-footer {
        flex-direction: column;
    }
    
    .modal-btn {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .modal-btn:last-child {
        margin-bottom: 0;
    }
}

/* Animation for modal entrance */
@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.7) translateY(-50px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Loading state for buttons */
.modal-btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

.modal-btn.loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Focus styles for accessibility */
.modal-btn:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Reservation Details Modal Specific Styles */
.reservation-details-modal {
    max-width: 600px;
}

.reservation-details-modal .modal-header.info {
    background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
    color: white;
    position: relative;
}

.reservation-details-modal .info-icon {
    font-size: 1.5rem;
    margin-right: 0.5rem;
}

.reservation-details-modal .modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 50%;
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
}

.reservation-details-modal .modal-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.reservations-list {
    max-height: 400px;
    overflow-y: auto;
}

.reservation-item {
    padding: 1rem 0;
}

.reservation-item.border-top {
    border-top: 1px solid #e0e0e0;
    margin-top: 1rem;
    padding-top: 1rem;
}

.reservation-item h4 {
    color: #333;
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.reservation-item h4 i {
    color: #2196f3;
}

.reservation-details p {
    margin: 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666;
}

.reservation-details i {
    color: #888;
    width: 1rem;
    text-align: center;
}

.reservation-details strong {
    color: #333;
    margin-right: 0.25rem;
}

.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-badge.status-pending {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-badge.status-confirmed {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-badge.status-cancelled {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.no-reservations {
    text-align: center;
    color: #888;
    font-style: italic;
    padding: 2rem;
}

.selection-note {
    background-color: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 6px;
    padding: 1rem;
    margin-top: 1rem;
    color: #1565c0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.selection-note i {
    color: #2196f3;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .modal-content {
        background: #2d3748;
        color: #e2e8f0;
    }

    .modal-header {
        border-bottom-color: #4a5568;
    }

    .modal-header h3 {
        color: #e2e8f0;
    }

    .modal-body p {
        color: #cbd5e0;
    }
}
