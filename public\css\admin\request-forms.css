/**
 * Admin Request Forms Styles
 * Clean, organized styles for the admin request forms page
 */

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Page Header */
.page-header {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin-bottom: 30px;
    border-left: 5px solid #2e7d32;
}

.page-header h1 {
    margin: 0;
    color: #2e7d32;
    font-size: 32px;
    font-weight: 700;
}

.page-header p {
    margin: 10px 0 0 0;
    color: #666;
    font-size: 16px;
}

/* Statistics Cards */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.2s;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-card .stat-number {
    font-size: 28px;
    font-weight: bold;
    color: #2e7d32;
}

.stat-card .stat-label {
    color: #666;
    font-size: 14px;
    margin-top: 8px;
    font-weight: 500;
}

/* Form Cards */
.forms-grid {
    display: grid;
    gap: 20px;
}

.form-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    padding: 25px;
    transition: transform 0.2s;
    border-left: 5px solid #2e7d32;
}

.form-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}

.form-card.campus {
    border-left-color: #2e7d32;
}

.form-card.internal {
    border-left-color: #1976d2;
}

.form-card.external {
    border-left-color: #f57c00;
}

.form-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
}

.form-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.form-type-badge {
    background: #e8f5e8;
    color: #2e7d32;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
}

.form-type-badge.internal {
    background: #e3f2fd;
    color: #1976d2;
}

.form-type-badge.external {
    background: #fff3e0;
    color: #f57c00;
}

.form-details {
    margin-bottom: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.form-detail {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.form-detail strong {
    color: #555;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-detail span {
    color: #333;
    font-size: 14px;
}

/* Status Badges */
.status-badge {
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    display: inline-block;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-accepted {
    background: #d4edda;
    color: #155724;
}

.status-rejected {
    background: #f8d7da;
    color: #721c24;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 12px;
    margin-top: 20px;
    flex-wrap: wrap;
}

/* Buttons */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s;
}

.btn-primary {
    background: #2e7d32;
    color: white;
}

.btn-primary:hover {
    background: #1b5e20;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.empty-state img {
    width: 120px;
    opacity: 0.5;
    margin-bottom: 20px;
}

/* Loading */
.loading {
    text-align: center;
    padding: 40px;
}

.loading-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #2e7d32;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 30px;
}

.pagination button {
    padding: 10px 16px;
    border: 2px solid #e0e0e0;
    background: white;
    cursor: pointer;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s;
}

.pagination button:hover:not(:disabled) {
    background: #f8f9fa;
    border-color: #2e7d32;
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination .current-page {
    background: #2e7d32;
    color: white;
    border-color: #2e7d32;
}

/* Footer */
.footer {
    background-color: #033e2e;
    color: white;
    text-align: center;
    padding: 20px;
    font-size: 14px;
    margin-top: 50px;
}
